#!/usr/bin/env python3
"""
Test script for validation and auto-save features in Report Maker v2
"""

import sys
import os
import time
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton, 
    QHBoxLayout, QTextEdit, QMessageBox, QInputDialog
)
from PyQt5.QtCore import Qt, QTimer

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from enhanced_widgets import EnhancedDataTable
    from responsive_ui import apply_responsive_stylesheet
    print("✓ Validation and auto-save components imported successfully")
    FEATURES_AVAILABLE = True
except ImportError as e:
    print(f"✗ Failed to import components: {e}")
    FEATURES_AVAILABLE = False


class ValidationAutoSaveTestWindow(QMainWindow):
    """Test window for validation and auto-save features"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Report Maker v2 - Validation & Auto-Save Test")
        self.setMinimumSize(900, 700)
        self.resize(1200, 800)
        
        if FEATURES_AVAILABLE:
            self.setStyleSheet(apply_responsive_stylesheet())
            self.setup_test()
        else:
            self.setup_fallback()
    
    def setup_test(self):
        """Setup validation and auto-save test"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Title
        title = QLabel("Validation & Auto-Save Features Test")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 18px; font-weight: bold; margin: 10px;")
        layout.addWidget(title)
        
        # Control buttons
        button_layout = QHBoxLayout()
        
        self.btn_test_validation = QPushButton("Test All Validations")
        self.btn_test_validation.clicked.connect(self.test_all_validations)
        button_layout.addWidget(self.btn_test_validation)
        
        self.btn_save_draft = QPushButton("Save Draft")
        self.btn_save_draft.clicked.connect(self.save_draft)
        button_layout.addWidget(self.btn_save_draft)
        
        self.btn_load_draft = QPushButton("Load Draft")
        self.btn_load_draft.clicked.connect(self.load_draft)
        button_layout.addWidget(self.btn_load_draft)
        
        self.btn_validate_all = QPushButton("Validate All Data")
        self.btn_validate_all.clicked.connect(self.validate_all_data)
        button_layout.addWidget(self.btn_validate_all)
        
        self.btn_clear_errors = QPushButton("Clear Errors")
        self.btn_clear_errors.clicked.connect(self.clear_validation_errors)
        button_layout.addWidget(self.btn_clear_errors)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        # Enhanced data table with comprehensive validation
        self.table = EnhancedDataTable(auto_save_file="validation_test_autosave.json")
        self.table.setRowCount(10)
        self.table.setColumnCount(8)
        self.table.setHorizontalHeaderLabels([
            "Name", "Email", "Age", "Salary", "Start Date", "Category", "Phone", "Notes"
        ])
        
        # Add comprehensive validation rules
        self.setup_validation_rules()
        
        # Add column formatting
        self.table.add_column_format(3, 'currency')  # Salary
        self.table.add_column_format(4, 'date', {
            'input_format': '%Y-%m-%d',
            'output_format': '%m/%d/%Y'
        })  # Start Date
        
        layout.addWidget(self.table)
        
        # Status and log area
        status_layout = QHBoxLayout()
        
        # Status label
        self.status_label = QLabel("Ready - Test validation and auto-save features")
        self.status_label.setStyleSheet("font-weight: bold; margin: 5px;")
        status_layout.addWidget(self.status_label, 1)
        
        # Auto-save indicator
        self.autosave_label = QLabel("Auto-save: Ready")
        self.autosave_label.setStyleSheet("color: green; margin: 5px;")
        status_layout.addWidget(self.autosave_label)
        
        layout.addLayout(status_layout)
        
        # Log area
        self.log_area = QTextEdit()
        self.log_area.setMaximumHeight(150)
        self.log_area.setPlaceholderText("Validation and auto-save logs will appear here...")
        layout.addWidget(self.log_area)
        
        # Connect signals
        self.table.data_changed.connect(self.on_data_changed)
        
        # Setup auto-save timer indicator
        self.autosave_timer = QTimer()
        self.autosave_timer.timeout.connect(self.update_autosave_status)
        self.autosave_timer.start(1000)  # Update every second
        
        # Highlight required fields
        self.table.highlight_required_fields()
        
        self.log("Validation and auto-save test setup complete")
        self.log("Try entering invalid data to see validation in action")
    
    def setup_validation_rules(self):
        """Setup comprehensive validation rules"""
        # Name - required, length 2-50
        self.table.add_validation_rule(0, 'required', error_message="Name is required")
        self.table.add_validation_rule(0, 'length', {'min': 2, 'max': 50}, "Name must be 2-50 characters")
        
        # Email - required, email format
        self.table.add_validation_rule(1, 'required', error_message="Email is required")
        self.table.add_validation_rule(1, 'email', error_message="Invalid email format")
        
        # Age - numeric, range 18-100
        self.table.add_validation_rule(2, 'numeric', {'min': 18, 'max': 100}, "Age must be 18-100")
        
        # Salary - numeric, minimum 0
        self.table.add_validation_rule(3, 'numeric', {'min': 0}, "Salary must be positive")
        
        # Start Date - date format
        self.table.add_validation_rule(4, 'date', {'format': '%Y-%m-%d'}, "Date format: YYYY-MM-DD")
        
        # Category - choice from predefined list
        categories = ['Engineering', 'Sales', 'Marketing', 'HR', 'Finance']
        self.table.add_validation_rule(5, 'choice', {'choices': categories}, 
                                     f"Must be one of: {', '.join(categories)}")
        
        # Phone - regex pattern
        phone_pattern = r'^\+?1?-?\(?[0-9]{3}\)?-?[0-9]{3}-?[0-9]{4}$'
        self.table.add_validation_rule(6, 'regex', {'value': phone_pattern}, 
                                     "Phone format: (************* or ************")
        
        # Notes - length limit
        self.table.add_validation_rule(7, 'length', {'max': 200}, "Notes max 200 characters")
    
    def setup_fallback(self):
        """Setup fallback when features not available"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        label = QLabel("Validation and auto-save features not available")
        label.setAlignment(Qt.AlignCenter)
        label.setStyleSheet("color: red; font-size: 16px; margin: 20px;")
        layout.addWidget(label)
    
    def test_all_validations(self):
        """Test all validation types with sample data"""
        test_data = [
            # Valid data
            ["John Doe", "<EMAIL>", "30", "75000", "2024-01-15", "Engineering", "************", "Good employee"],
            # Invalid data
            ["", "invalid-email", "15", "-1000", "invalid-date", "InvalidCategory", "invalid-phone", "A" * 250],
            ["A", "test@", "150", "abc", "2024-13-45", "Unknown", "123", ""],
        ]
        
        for row, row_data in enumerate(test_data):
            for col, cell_data in enumerate(row_data):
                from PyQt5.QtWidgets import QTableWidgetItem
                item = QTableWidgetItem(str(cell_data))
                self.table.setItem(row, col, item)
        
        self.log("Test data added - check validation highlighting")
        self.status_label.setText("Test data added - validation errors should be highlighted")
    
    def save_draft(self):
        """Save current data as a draft"""
        draft_name, ok = QInputDialog.getText(self, "Save Draft", "Enter draft name:")
        if ok and draft_name:
            if self.table.create_draft_save(draft_name):
                self.log(f"Draft '{draft_name}' saved successfully")
                QMessageBox.information(self, "Success", f"Draft '{draft_name}' saved!")
            else:
                self.log(f"Failed to save draft '{draft_name}'")
                QMessageBox.warning(self, "Error", "Failed to save draft")
    
    def load_draft(self):
        """Load a saved draft"""
        drafts = self.table.get_available_drafts()
        if not drafts:
            QMessageBox.information(self, "No Drafts", "No saved drafts found")
            return
        
        from PyQt5.QtWidgets import QInputDialog
        draft_name, ok = QInputDialog.getItem(self, "Load Draft", "Select draft:", drafts, 0, False)
        if ok and draft_name:
            if self.table.load_draft(draft_name):
                self.log(f"Draft '{draft_name}' loaded successfully")
                QMessageBox.information(self, "Success", f"Draft '{draft_name}' loaded!")
            else:
                self.log(f"Failed to load draft '{draft_name}'")
                QMessageBox.warning(self, "Error", "Failed to load draft")
    
    def validate_all_data(self):
        """Validate all data and show summary"""
        summary = self.table.get_validation_summary()
        self.log("Validation summary generated")
        QMessageBox.information(self, "Validation Summary", summary)
    
    def clear_validation_errors(self):
        """Clear all validation error highlighting"""
        self.table.clear_validation_errors()
        self.log("Validation errors cleared")
        self.status_label.setText("Validation errors cleared")
    
    def on_data_changed(self):
        """Handle data changes"""
        self.status_label.setText("Data modified - auto-save will trigger in 30 seconds")
        self.log("Data changed - marked for auto-save")
    
    def update_autosave_status(self):
        """Update auto-save status indicator"""
        # This is a simplified indicator - in real implementation, 
        # you'd track the actual auto-save timer
        if hasattr(self.table, 'is_modified') and self.table.is_modified:
            self.autosave_label.setText("Auto-save: Pending")
            self.autosave_label.setStyleSheet("color: orange; margin: 5px;")
        else:
            self.autosave_label.setText("Auto-save: Saved")
            self.autosave_label.setStyleSheet("color: green; margin: 5px;")
    
    def log(self, message):
        """Add message to log area"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_area.append(f"[{timestamp}] {message}")


def main():
    """Main test function"""
    print("=== Report Maker v2 Validation & Auto-Save Test ===")
    
    app = QApplication.instance() or QApplication(sys.argv)
    
    window = ValidationAutoSaveTestWindow()
    window.show()
    
    print(f"Features Available: {FEATURES_AVAILABLE}")
    
    if FEATURES_AVAILABLE:
        print("\n=== Test Instructions ===")
        print("1. Click 'Test All Validations' to see validation in action")
        print("2. Try entering invalid data manually")
        print("3. Use 'Save Draft' and 'Load Draft' to test draft functionality")
        print("4. Click 'Validate All Data' to see validation summary")
        print("5. Watch auto-save status indicator")
        print("6. Check the log area for detailed information")
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()

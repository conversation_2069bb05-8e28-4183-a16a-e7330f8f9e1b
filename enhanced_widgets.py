"""
Enhanced UI Widgets for Report Maker v2
Provides specialized widgets with improved functionality and responsiveness
"""

import os
import json
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QHeaderView, QAbstractItemView, QComboBox, QLineEdit, QPushButton,
    QLabel, QGroupBox, QSplitter, QMessageBox, QFileDialog, QProgressBar,
    QApplication, QSizePolicy, QFrame, QScrollArea
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread, QMutex
from PyQt5.QtGui import QFont, QPalette, QColor
from responsive_ui import ExcelLikeTableWidget, ResponsiveWidget, ScrollableContainer


class EnhancedDataTable(ExcelLikeTableWidget):
    """Enhanced data table with auto-save and validation"""

    def __init__(self, parent=None, auto_save_file=None):
        super().__init__(parent)
        self.auto_save_file = auto_save_file
        self.validation_rules = {}
        self.is_modified = False
        self.column_formats = {}  # Store column formatting rules
        self.filter_enabled = False
        self.sort_enabled = True

        # Ensure undo_stack is available (inherited from parent)
        if not hasattr(self, 'undo_stack'):
            from PyQt5.QtWidgets import QUndoStack
            self.undo_stack = QUndoStack(self)

        # Setup validation
        self.itemChanged.connect(self.validate_item)

        # Setup auto-save
        if auto_save_file:
            self.load_auto_save()

        # Enable additional Excel-like features
        self.setup_advanced_features()

    def setup_advanced_features(self):
        """Setup advanced Excel-like features"""
        # Enable sorting
        self.setSortingEnabled(self.sort_enabled)

        # Setup column formatting
        self.setup_column_formatting()

        # Setup data filtering
        self.setup_data_filtering()

        # Setup cell formatting
        self.setup_cell_formatting()

    def setup_column_formatting(self):
        """Setup column-specific formatting"""
        # This will be called when columns are set up
        pass

    def setup_data_filtering(self):
        """Setup data filtering capabilities"""
        # Add filter functionality to headers
        header = self.horizontalHeader()
        header.sectionClicked.connect(self.on_header_clicked)

    def setup_cell_formatting(self):
        """Setup cell formatting options"""
        # Enable rich text editing for cells
        self.setEditTriggers(
            QAbstractItemView.DoubleClicked |
            QAbstractItemView.EditKeyPressed |
            QAbstractItemView.AnyKeyPressed |
            QAbstractItemView.SelectedClicked
        )

    def on_header_clicked(self, logical_index):
        """Handle header clicks for sorting/filtering"""
        if self.filter_enabled:
            self.show_column_filter(logical_index)

    def show_column_filter(self, column):
        """Show filter dialog for a column"""
        # Get unique values in the column
        unique_values = set()
        for row in range(self.rowCount()):
            item = self.item(row, column)
            if item:
                unique_values.add(item.text())

        # Create filter dialog (simplified implementation)
        from PyQt5.QtWidgets import QInputDialog
        filter_text, ok = QInputDialog.getText(
            self, f"Filter Column {column + 1}",
            "Enter filter text (leave empty to clear filter):"
        )

        if ok:
            self.apply_column_filter(column, filter_text)

    def apply_column_filter(self, column, filter_text):
        """Apply filter to a column"""
        for row in range(self.rowCount()):
            item = self.item(row, column)
            if item:
                if not filter_text or filter_text.lower() in item.text().lower():
                    self.setRowHidden(row, False)
                else:
                    self.setRowHidden(row, True)

    def add_column_format(self, column, format_type, format_options=None):
        """Add formatting rule for a column"""
        self.column_formats[column] = {
            'type': format_type,
            'options': format_options or {}
        }

    def format_cell_value(self, row, column, value):
        """Format cell value based on column rules"""
        if column not in self.column_formats:
            return value

        format_rule = self.column_formats[column]
        format_type = format_rule['type']

        try:
            if format_type == 'currency':
                return f"${float(value):,.2f}" if value else "$0.00"
            elif format_type == 'percentage':
                return f"{float(value):.1f}%" if value else "0.0%"
            elif format_type == 'date':
                from datetime import datetime
                if value:
                    date_obj = datetime.strptime(str(value), format_rule['options'].get('input_format', '%Y-%m-%d'))
                    return date_obj.strftime(format_rule['options'].get('output_format', '%m/%d/%Y'))
                return ""
            elif format_type == 'number':
                decimals = format_rule['options'].get('decimals', 2)
                return f"{float(value):,.{decimals}f}" if value else "0"
        except (ValueError, TypeError):
            pass

        return value
    
    def add_validation_rule(self, column, rule_type, rule_value=None, error_message="Invalid input"):
        """Add validation rule for a column"""
        self.validation_rules[column] = {
            'type': rule_type,
            'value': rule_value,
            'message': error_message
        }
    
    def validate_item(self, item):
        """Enhanced validation with comprehensive error handling"""
        column = item.column()
        row = item.row()
        text = item.text().strip()

        # Clear previous error styling first
        item.setBackground(QColor(255, 255, 255))
        item.setToolTip("")

        if column not in self.validation_rules:
            self.is_modified = True
            return True

        rule = self.validation_rules[column]
        error_message = rule.get('message', 'Invalid input')

        # Validate based on rule type
        try:
            if rule['type'] == 'required' and not text:
                self.show_validation_error(item, error_message)
                return False

            elif rule['type'] == 'numeric' and text:
                try:
                    value = float(text)
                    # Check for range validation
                    if 'min' in rule and value < rule['min']:
                        self.show_validation_error(item, f"Value must be >= {rule['min']}")
                        return False
                    if 'max' in rule and value > rule['max']:
                        self.show_validation_error(item, f"Value must be <= {rule['max']}")
                        return False
                except ValueError:
                    self.show_validation_error(item, error_message)
                    return False

            elif rule['type'] == 'email' and text:
                import re
                email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
                if not re.match(email_pattern, text):
                    self.show_validation_error(item, "Invalid email format")
                    return False

            elif rule['type'] == 'date' and text:
                from datetime import datetime
                date_format = rule.get('format', '%Y-%m-%d')
                try:
                    datetime.strptime(text, date_format)
                except ValueError:
                    self.show_validation_error(item, f"Date must be in format {date_format}")
                    return False

            elif rule['type'] == 'regex' and rule.get('value') and text:
                import re
                if not re.match(rule['value'], text):
                    self.show_validation_error(item, error_message)
                    return False

            elif rule['type'] == 'length' and text:
                min_len = rule.get('min', 0)
                max_len = rule.get('max', float('inf'))
                if len(text) < min_len:
                    self.show_validation_error(item, f"Minimum length: {min_len}")
                    return False
                if len(text) > max_len:
                    self.show_validation_error(item, f"Maximum length: {max_len}")
                    return False

            elif rule['type'] == 'choice' and text:
                choices = rule.get('choices', [])
                if text not in choices:
                    self.show_validation_error(item, f"Must be one of: {', '.join(choices)}")
                    return False

            # Custom validation function
            elif rule['type'] == 'custom' and rule.get('function') and text:
                validation_func = rule['function']
                if not validation_func(text):
                    self.show_validation_error(item, error_message)
                    return False

        except Exception as e:
            print(f"Validation error: {e}")
            return False

        # Validation passed
        self.is_modified = True
        return True
    
    def show_validation_error(self, item, message):
        """Enhanced validation error display"""
        # Set error background color
        item.setBackground(QColor(255, 200, 200))

        # Set detailed tooltip
        item.setToolTip(f"❌ Validation Error: {message}")

        # Optional: Add error icon or border
        item.setData(Qt.UserRole, "validation_error")

        # Log validation error for debugging
        row = item.row()
        col = item.column()
        print(f"Validation error at ({row}, {col}): {message}")

    def clear_validation_errors(self):
        """Clear all validation errors in the table"""
        for row in range(self.rowCount()):
            for col in range(self.columnCount()):
                item = self.item(row, col)
                if item and item.data(Qt.UserRole) == "validation_error":
                    item.setBackground(QColor(255, 255, 255))
                    item.setToolTip("")
                    item.setData(Qt.UserRole, None)

    def validate_all_data(self):
        """Validate all data in the table"""
        errors = []
        for row in range(self.rowCount()):
            for col in range(self.columnCount()):
                item = self.item(row, col)
                if item:
                    if not self.validate_item(item):
                        errors.append(f"Row {row + 1}, Column {col + 1}: {item.toolTip()}")
        return errors

    def get_validation_summary(self):
        """Get summary of validation errors"""
        errors = self.validate_all_data()
        if not errors:
            return "✓ All data is valid"
        else:
            return f"❌ {len(errors)} validation error(s) found:\n" + "\n".join(errors[:10])

    def highlight_required_fields(self):
        """Highlight required fields that are empty"""
        for col, rule in self.validation_rules.items():
            if rule['type'] == 'required':
                header_item = self.horizontalHeaderItem(col)
                if header_item:
                    header_item.setText(f"{header_item.text()} *")
                    header_item.setToolTip("This field is required")
    
    def auto_save(self):
        """Auto-save table data with enhanced features"""
        if not self.auto_save_file or not self.is_modified:
            return

        try:
            import datetime

            data = []
            for row in range(self.rowCount()):
                row_data = []
                for col in range(self.columnCount()):
                    item = self.item(row, col)
                    cell_value = item.text() if item else ""
                    # Apply formatting if available
                    if col in self.column_formats:
                        cell_value = self.format_cell_value(row, col, cell_value)
                    row_data.append(cell_value)
                data.append(row_data)

            # Save headers
            headers = []
            for col in range(self.columnCount()):
                header_item = self.horizontalHeaderItem(col)
                headers.append(header_item.text() if header_item else f"Column {col + 1}")

            # Enhanced save data with metadata
            save_data = {
                'headers': headers,
                'data': data,
                'timestamp': datetime.datetime.now().isoformat(),
                'row_count': self.rowCount(),
                'column_count': self.columnCount(),
                'validation_rules': self.validation_rules,
                'column_formats': self.column_formats,
                'version': '2.0'
            }

            # Create backup of previous save
            backup_file = f"{self.auto_save_file}.backup"
            if os.path.exists(self.auto_save_file):
                import shutil
                shutil.copy2(self.auto_save_file, backup_file)

            with open(self.auto_save_file, 'w') as f:
                json.dump(save_data, f, indent=2)

            self.is_modified = False
            print(f"Auto-saved to {self.auto_save_file}")

        except Exception as e:
            print(f"Auto-save failed: {e}")

    def create_draft_save(self, draft_name):
        """Create a named draft save"""
        if not draft_name:
            return False

        try:
            draft_file = f"draft_{draft_name}.json"
            temp_auto_save = self.auto_save_file
            self.auto_save_file = draft_file
            self.auto_save()
            self.auto_save_file = temp_auto_save
            return True
        except Exception as e:
            print(f"Draft save failed: {e}")
            return False

    def load_draft(self, draft_name):
        """Load a named draft"""
        try:
            draft_file = f"draft_{draft_name}.json"
            if os.path.exists(draft_file):
                temp_auto_save = self.auto_save_file
                self.auto_save_file = draft_file
                self.load_auto_save()
                self.auto_save_file = temp_auto_save
                return True
            return False
        except Exception as e:
            print(f"Draft load failed: {e}")
            return False

    def get_available_drafts(self):
        """Get list of available draft saves"""
        try:
            drafts = []
            for file in os.listdir('.'):
                if file.startswith('draft_') and file.endswith('.json'):
                    draft_name = file[6:-5]  # Remove 'draft_' prefix and '.json' suffix
                    drafts.append(draft_name)
            return sorted(drafts)
        except Exception as e:
            print(f"Error getting drafts: {e}")
            return []
    
    def load_auto_save(self):
        """Load auto-saved data"""
        if not os.path.exists(self.auto_save_file):
            return
        
        try:
            with open(self.auto_save_file, 'r') as f:
                save_data = json.load(f)
            
            headers = save_data.get('headers', [])
            data = save_data.get('data', [])
            
            if not data:
                return
            
            # Set table dimensions
            self.setRowCount(len(data))
            self.setColumnCount(len(headers))
            self.setHorizontalHeaderLabels(headers)
            
            # Load data
            for row, row_data in enumerate(data):
                for col, cell_data in enumerate(row_data):
                    item = QTableWidgetItem(str(cell_data))
                    self.setItem(row, col, item)
            
            self.is_modified = False
            
        except Exception as e:
            print(f"Auto-save load failed: {e}")


class ResponsiveFormWidget(ResponsiveWidget):
    """Responsive form widget that adapts layout based on screen size"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.form_fields = []
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the responsive form UI"""
        self.main_layout = QVBoxLayout(self)
        self.form_container = QWidget()
        self.form_layout = QVBoxLayout(self.form_container)
        
        # Add scroll area for small screens
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidget(self.form_container)
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setFrameStyle(QFrame.NoFrame)
        
        self.main_layout.addWidget(self.scroll_area)
    
    def add_form_field(self, label_text, widget, required=False):
        """Add a form field with responsive layout"""
        field_container = QWidget()
        field_layout = QHBoxLayout(field_container)
        
        # Create label
        label = QLabel(label_text)
        if required:
            label.setText(f"{label_text} *")
            label.setStyleSheet("color: #e74c3c; font-weight: bold;")
        
        # Set responsive sizing
        label.setMinimumWidth(120)
        widget.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        
        field_layout.addWidget(label)
        field_layout.addWidget(widget, 1)
        
        self.form_layout.addWidget(field_container)
        self.form_fields.append({'label': label, 'widget': widget, 'required': required})
    
    def update_responsive_layout(self):
        """Update layout based on screen size"""
        screen_category = self.get_screen_category()
        
        for field in self.form_fields:
            container = field['widget'].parent()
            layout = container.layout()
            
            if screen_category == "mobile":
                # Stack vertically on mobile
                layout.setDirection(QVBoxLayout.TopToBottom)
                field['label'].setMinimumWidth(0)
            else:
                # Horizontal layout on larger screens
                layout.setDirection(QHBoxLayout.LeftToRight)
                field['label'].setMinimumWidth(120)
    
    def validate_form(self):
        """Validate all form fields"""
        errors = []
        
        for field in self.form_fields:
            if field['required']:
                widget = field['widget']
                if isinstance(widget, QLineEdit) and not widget.text().strip():
                    errors.append(f"{field['label'].text()} is required")
                elif isinstance(widget, QComboBox) and widget.currentIndex() == 0:
                    errors.append(f"{field['label'].text()} must be selected")
        
        if errors:
            QMessageBox.warning(self, "Validation Error", "\n".join(errors))
            return False
        
        return True


class EnhancedProgressWidget(QWidget):
    """Enhanced progress widget with better visual feedback"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the progress widget UI"""
        layout = QVBoxLayout(self)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setTextVisible(True)
        layout.addWidget(self.progress_bar)
        
        # Status label
        self.status_label = QLabel("Ready")
        self.status_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.status_label)
        
        # Details label (for additional info)
        self.details_label = QLabel("")
        self.details_label.setWordWrap(True)
        self.details_label.setStyleSheet("color: #7f8c8d; font-size: 12px;")
        layout.addWidget(self.details_label)
    
    def set_progress(self, value, maximum=100, status="", details=""):
        """Set progress with status and details"""
        self.progress_bar.setMaximum(maximum)
        self.progress_bar.setValue(value)
        
        if status:
            self.status_label.setText(status)
        
        if details:
            self.details_label.setText(details)
        
        # Update progress bar text
        percentage = int((value / maximum) * 100) if maximum > 0 else 0
        self.progress_bar.setFormat(f"{percentage}% ({value}/{maximum})")
        
        # Process events to update UI
        QApplication.processEvents()
    
    def reset(self):
        """Reset progress widget"""
        self.progress_bar.setValue(0)
        self.status_label.setText("Ready")
        self.details_label.setText("")


class ResponsiveFileSelector(ResponsiveWidget):
    """Responsive file selector widget"""
    
    file_selected = pyqtSignal(str)
    
    def __init__(self, parent=None, file_filter="All Files (*)"):
        super().__init__(parent)
        self.file_filter = file_filter
        self.selected_file = ""
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the file selector UI"""
        layout = QHBoxLayout(self)
        
        # File path display
        self.file_label = QLabel("No file selected")
        self.file_label.setStyleSheet("border: 1px solid #ddd; padding: 6px; background: white;")
        self.file_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        
        # Browse button
        self.browse_button = QPushButton("Browse...")
        self.browse_button.clicked.connect(self.browse_file)
        self.browse_button.setMinimumWidth(100)
        
        layout.addWidget(self.file_label, 1)
        layout.addWidget(self.browse_button)
    
    def browse_file(self):
        """Open file browser"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Select File", "", self.file_filter
        )
        
        if file_path:
            self.selected_file = file_path
            self.file_label.setText(os.path.basename(file_path))
            self.file_label.setToolTip(file_path)
            self.file_selected.emit(file_path)
    
    def update_responsive_layout(self):
        """Update layout for different screen sizes"""
        screen_category = self.get_screen_category()
        
        if screen_category == "mobile":
            # Stack vertically on mobile
            self.layout().setDirection(QVBoxLayout.TopToBottom)
            self.browse_button.setMinimumWidth(0)
        else:
            # Horizontal layout on larger screens
            self.layout().setDirection(QHBoxLayout.LeftToRight)
            self.browse_button.setMinimumWidth(100)
    
    def get_selected_file(self):
        """Get the selected file path"""
        return self.selected_file
    
    def set_file(self, file_path):
        """Set the file path programmatically"""
        if os.path.exists(file_path):
            self.selected_file = file_path
            self.file_label.setText(os.path.basename(file_path))
            self.file_label.setToolTip(file_path)
            self.file_selected.emit(file_path)

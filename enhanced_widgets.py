"""
Enhanced UI Widgets for Report Maker v2
Provides specialized widgets with improved functionality and responsiveness
"""

import os
import json
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QHeaderView, QAbstractItemView, QComboBox, QLineEdit, QPushButton,
    QLabel, QGroupBox, QSplitter, QMessageBox, QFileDialog, QProgressBar,
    QApplication, QSizePolicy, QFrame, QScrollArea
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread, QMutex
from PyQt5.QtGui import QFont, QPalette, QColor
from responsive_ui import ExcelLikeTableWidget, ResponsiveWidget, ScrollableContainer


class EnhancedDataTable(ExcelLikeTableWidget):
    """Enhanced data table with auto-save and validation"""
    
    def __init__(self, parent=None, auto_save_file=None):
        super().__init__(parent)
        self.auto_save_file = auto_save_file
        self.validation_rules = {}
        self.is_modified = False
        
        # Setup validation
        self.itemChanged.connect(self.validate_item)
        
        # Setup auto-save
        if auto_save_file:
            self.load_auto_save()
    
    def add_validation_rule(self, column, rule_type, rule_value=None, error_message="Invalid input"):
        """Add validation rule for a column"""
        self.validation_rules[column] = {
            'type': rule_type,
            'value': rule_value,
            'message': error_message
        }
    
    def validate_item(self, item):
        """Validate item based on column rules"""
        column = item.column()
        if column not in self.validation_rules:
            return True
        
        rule = self.validation_rules[column]
        text = item.text()
        
        # Validate based on rule type
        if rule['type'] == 'required' and not text.strip():
            self.show_validation_error(item, rule['message'])
            return False
        elif rule['type'] == 'numeric':
            try:
                float(text) if text else 0
            except ValueError:
                self.show_validation_error(item, rule['message'])
                return False
        elif rule['type'] == 'regex' and rule['value']:
            import re
            if not re.match(rule['value'], text):
                self.show_validation_error(item, rule['message'])
                return False
        
        # Clear any previous error styling
        item.setBackground(QColor(255, 255, 255))
        self.is_modified = True
        return True
    
    def show_validation_error(self, item, message):
        """Show validation error"""
        item.setBackground(QColor(255, 200, 200))
        item.setToolTip(message)
    
    def auto_save(self):
        """Auto-save table data"""
        if not self.auto_save_file or not self.is_modified:
            return
        
        try:
            data = []
            for row in range(self.rowCount()):
                row_data = []
                for col in range(self.columnCount()):
                    item = self.item(row, col)
                    row_data.append(item.text() if item else "")
                data.append(row_data)
            
            # Save headers
            headers = []
            for col in range(self.columnCount()):
                header_item = self.horizontalHeaderItem(col)
                headers.append(header_item.text() if header_item else f"Column {col + 1}")
            
            save_data = {
                'headers': headers,
                'data': data,
                'timestamp': QTimer().remainingTime()
            }
            
            with open(self.auto_save_file, 'w') as f:
                json.dump(save_data, f, indent=2)
            
            self.is_modified = False
            
        except Exception as e:
            print(f"Auto-save failed: {e}")
    
    def load_auto_save(self):
        """Load auto-saved data"""
        if not os.path.exists(self.auto_save_file):
            return
        
        try:
            with open(self.auto_save_file, 'r') as f:
                save_data = json.load(f)
            
            headers = save_data.get('headers', [])
            data = save_data.get('data', [])
            
            if not data:
                return
            
            # Set table dimensions
            self.setRowCount(len(data))
            self.setColumnCount(len(headers))
            self.setHorizontalHeaderLabels(headers)
            
            # Load data
            for row, row_data in enumerate(data):
                for col, cell_data in enumerate(row_data):
                    item = QTableWidgetItem(str(cell_data))
                    self.setItem(row, col, item)
            
            self.is_modified = False
            
        except Exception as e:
            print(f"Auto-save load failed: {e}")


class ResponsiveFormWidget(ResponsiveWidget):
    """Responsive form widget that adapts layout based on screen size"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.form_fields = []
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the responsive form UI"""
        self.main_layout = QVBoxLayout(self)
        self.form_container = QWidget()
        self.form_layout = QVBoxLayout(self.form_container)
        
        # Add scroll area for small screens
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidget(self.form_container)
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setFrameStyle(QFrame.NoFrame)
        
        self.main_layout.addWidget(self.scroll_area)
    
    def add_form_field(self, label_text, widget, required=False):
        """Add a form field with responsive layout"""
        field_container = QWidget()
        field_layout = QHBoxLayout(field_container)
        
        # Create label
        label = QLabel(label_text)
        if required:
            label.setText(f"{label_text} *")
            label.setStyleSheet("color: #e74c3c; font-weight: bold;")
        
        # Set responsive sizing
        label.setMinimumWidth(120)
        widget.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        
        field_layout.addWidget(label)
        field_layout.addWidget(widget, 1)
        
        self.form_layout.addWidget(field_container)
        self.form_fields.append({'label': label, 'widget': widget, 'required': required})
    
    def update_responsive_layout(self):
        """Update layout based on screen size"""
        screen_category = self.get_screen_category()
        
        for field in self.form_fields:
            container = field['widget'].parent()
            layout = container.layout()
            
            if screen_category == "mobile":
                # Stack vertically on mobile
                layout.setDirection(QVBoxLayout.TopToBottom)
                field['label'].setMinimumWidth(0)
            else:
                # Horizontal layout on larger screens
                layout.setDirection(QHBoxLayout.LeftToRight)
                field['label'].setMinimumWidth(120)
    
    def validate_form(self):
        """Validate all form fields"""
        errors = []
        
        for field in self.form_fields:
            if field['required']:
                widget = field['widget']
                if isinstance(widget, QLineEdit) and not widget.text().strip():
                    errors.append(f"{field['label'].text()} is required")
                elif isinstance(widget, QComboBox) and widget.currentIndex() == 0:
                    errors.append(f"{field['label'].text()} must be selected")
        
        if errors:
            QMessageBox.warning(self, "Validation Error", "\n".join(errors))
            return False
        
        return True


class EnhancedProgressWidget(QWidget):
    """Enhanced progress widget with better visual feedback"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the progress widget UI"""
        layout = QVBoxLayout(self)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setTextVisible(True)
        layout.addWidget(self.progress_bar)
        
        # Status label
        self.status_label = QLabel("Ready")
        self.status_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.status_label)
        
        # Details label (for additional info)
        self.details_label = QLabel("")
        self.details_label.setWordWrap(True)
        self.details_label.setStyleSheet("color: #7f8c8d; font-size: 12px;")
        layout.addWidget(self.details_label)
    
    def set_progress(self, value, maximum=100, status="", details=""):
        """Set progress with status and details"""
        self.progress_bar.setMaximum(maximum)
        self.progress_bar.setValue(value)
        
        if status:
            self.status_label.setText(status)
        
        if details:
            self.details_label.setText(details)
        
        # Update progress bar text
        percentage = int((value / maximum) * 100) if maximum > 0 else 0
        self.progress_bar.setFormat(f"{percentage}% ({value}/{maximum})")
        
        # Process events to update UI
        QApplication.processEvents()
    
    def reset(self):
        """Reset progress widget"""
        self.progress_bar.setValue(0)
        self.status_label.setText("Ready")
        self.details_label.setText("")


class ResponsiveFileSelector(ResponsiveWidget):
    """Responsive file selector widget"""
    
    file_selected = pyqtSignal(str)
    
    def __init__(self, parent=None, file_filter="All Files (*)"):
        super().__init__(parent)
        self.file_filter = file_filter
        self.selected_file = ""
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the file selector UI"""
        layout = QHBoxLayout(self)
        
        # File path display
        self.file_label = QLabel("No file selected")
        self.file_label.setStyleSheet("border: 1px solid #ddd; padding: 6px; background: white;")
        self.file_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        
        # Browse button
        self.browse_button = QPushButton("Browse...")
        self.browse_button.clicked.connect(self.browse_file)
        self.browse_button.setMinimumWidth(100)
        
        layout.addWidget(self.file_label, 1)
        layout.addWidget(self.browse_button)
    
    def browse_file(self):
        """Open file browser"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Select File", "", self.file_filter
        )
        
        if file_path:
            self.selected_file = file_path
            self.file_label.setText(os.path.basename(file_path))
            self.file_label.setToolTip(file_path)
            self.file_selected.emit(file_path)
    
    def update_responsive_layout(self):
        """Update layout for different screen sizes"""
        screen_category = self.get_screen_category()
        
        if screen_category == "mobile":
            # Stack vertically on mobile
            self.layout().setDirection(QVBoxLayout.TopToBottom)
            self.browse_button.setMinimumWidth(0)
        else:
            # Horizontal layout on larger screens
            self.layout().setDirection(QHBoxLayout.LeftToRight)
            self.browse_button.setMinimumWidth(100)
    
    def get_selected_file(self):
        """Get the selected file path"""
        return self.selected_file
    
    def set_file(self, file_path):
        """Set the file path programmatically"""
        if os.path.exists(file_path):
            self.selected_file = file_path
            self.file_label.setText(os.path.basename(file_path))
            self.file_label.setToolTip(file_path)
            self.file_selected.emit(file_path)

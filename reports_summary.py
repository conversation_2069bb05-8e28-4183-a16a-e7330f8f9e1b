# reports_summary.py

import sys
import os
import traceback
import pandas as pd
from PyQt5.QtWidgets import (
    QApplication, QDialog, QVBoxLayout, QHBoxLayout, QGridLayout, QPushButton,
    QLabel, QGroupBox, QTableWidget, QTableWidgetItem, QHeaderView, QLineEdit,
    QComboBox, QFileDialog, QMessageBox, QDialogButtonBox, QDateEdit, QAbstractItemView,
    QListWidget, QInputDialog
)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont

# --- Configuration ---
SUMMARY_DATA_FILE = "summary_data.xlsx"
INTEGRAL_DATABASE_FILE = "integral_database.xlsx"
TUBE_NUMBER_COL = 'Main_Header'
MECHANICAL_TEST_COL_PRIMARY = 'Sub_Header_1'
MECHANICAL_TEST_COL_FALLBACK = 'Sub_Header'

class SummaryDataHandler:
    """Handles reading from and writing to the summary data Excel file."""
    def __init__(self, filepath):
        self.filepath = filepath
        # Define default/required sheets
        self.required_sheets = {
            'QC_OK': pd.DataFrame(columns=['Sr no.', 'Tube No.', 'QC Done Date']),
            'QS_OK': pd.DataFrame(columns=['Sr no.', 'Tube No.', 'QS Done Date']),
            'UT_Available': pd.DataFrame(columns=['Sr no.', 'Tube No.']),
            'Miscellaneous': pd.DataFrame(columns=['S No.', 'Tube nos.', 'Date', 'Status', 'Remarks'])
        }
        self.sheets = self.required_sheets.copy()
        self.load_data()

    def load_data(self):
        # Reset to defaults before loading
        self.sheets = self.required_sheets.copy()
        if not os.path.exists(self.filepath):
            self.save_data()
            return
        try:
            xls = pd.ExcelFile(self.filepath)
            # Load all sheets from the file, overwriting defaults if they exist
            for sheet_name in xls.sheet_names:
                self.sheets[sheet_name] = pd.read_excel(xls, sheet_name=sheet_name, dtype=str).fillna('')
        except Exception as e:
            QMessageBox.critical(None, "Excel Load Error", f"Failed to load '{self.filepath}':\n{e}")

    def get_sheet(self, name):
        return self.sheets.get(name, pd.DataFrame()).copy()

    def update_sheet(self, name, df):
        self.sheets[name] = df
        self.save_data()
    
    def delete_sheet(self, name):
        if name in self.sheets:
            del self.sheets[name]
            self.save_data()

    def save_data(self):
        try:
            # Ensure required sheets exist before saving
            for name, default_df in self.required_sheets.items():
                if name not in self.sheets:
                    self.sheets[name] = default_df

            with pd.ExcelWriter(self.filepath, engine='openpyxl') as writer:
                for sheet_name, df in self.sheets.items():
                    # BUG FIX: Make serial number check more specific to avoid matching 'Tube No.'
                    s_col = next((col for col in df.columns if col.lower() in ['sr no.', 's no.']), None)
                    if s_col:
                        # Auto-renumber the serial column
                        df[s_col] = range(1, len(df) + 1)
                    df.to_excel(writer, sheet_name=sheet_name, index=False)
        except Exception as e:
            QMessageBox.critical(None, "Excel Save Error", f"Failed to save to '{self.filepath}':\n{e}")

class FlexibleListEditorDialog(QDialog):
    """A flexible dialog to edit a list in a table, similar to Excel."""
    def __init__(self, parent, df, title, column_configs, validation_func):
        super().__init__(parent)
        self.setWindowTitle(f"Edit List: {title}")
        self.setMinimumSize(800, 600)
        self.df = df
        self.column_configs = column_configs
        self.validation_func = validation_func
        self.updated_df = None

        layout = QVBoxLayout(self)
        self.table = QTableWidget()
        self.table.setColumnCount(len(df.columns))
        self.table.setHorizontalHeaderLabels(df.columns)
        self.populate_table()
        layout.addWidget(self.table)

        button_layout = QHBoxLayout()
        self.btn_add = QPushButton("Add Row")
        self.btn_add.clicked.connect(self.add_row)
        button_layout.addWidget(self.btn_add)

        self.btn_delete = QPushButton("Delete Selected Row(s)")
        self.btn_delete.clicked.connect(self.delete_selected_rows)
        button_layout.addWidget(self.btn_delete)
        button_layout.addStretch()
        layout.addLayout(button_layout)

        self.button_box = QDialogButtonBox(QDialogButtonBox.Save | QDialogButtonBox.Cancel)
        self.button_box.button(QDialogButtonBox.Save).setText("Save and Close")
        self.button_box.accepted.connect(self.save_and_close)
        self.button_box.rejected.connect(self.reject)
        layout.addWidget(self.button_box)

    def populate_table(self):
        self.table.setRowCount(len(self.df))
        df_for_display = self.df.astype(str)
        for r, row_data in df_for_display.iterrows():
            for c, col_name in enumerate(df_for_display.columns):
                self.place_cell_item(r, c, row_data[col_name])
        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)

    def place_cell_item(self, row, col, value):
        col_name = self.table.horizontalHeaderItem(col).text()
        config = self.column_configs.get(col_name, {})
        widget_type = config.get('type')

        if widget_type == 'date':
            date_edit = QDateEdit(calendarPopup=True)
            date_edit.setDisplayFormat("yyyy-MM-dd")
            qdate = QDate.fromString(value, "yyyy-MM-dd")
            date_edit.setDate(qdate if qdate.isValid() else QDate.currentDate())
            self.table.setCellWidget(row, col, date_edit)
        elif widget_type == 'combo':
            combo = QComboBox()
            combo.addItems(config.get('options', []))
            if value in config.get('options', []):
                combo.setCurrentText(value)
            self.table.setCellWidget(row, col, combo)
        else: # Default to a standard text item
            item = QTableWidgetItem(str(value))
            if widget_type == 'readonly':
                item.setFlags(item.flags() & ~Qt.ItemIsEditable)
            self.table.setItem(row, col, item)

    def add_row(self):
        row_position = self.table.rowCount()
        self.table.insertRow(row_position)
        for c in range(self.table.columnCount()):
            self.place_cell_item(row_position, c, "")

    def delete_selected_rows(self):
        selected_rows = sorted(list(set(index.row() for index in self.table.selectedIndexes())), reverse=True)
        if not selected_rows:
            QMessageBox.information(self, "No Selection", "Please select one or more rows to delete.")
            return
        for row in selected_rows:
            self.table.removeRow(row)

    def save_and_close(self):
        try:
            # Extract data from table into a list of dictionaries
            all_rows_data = []
            for r in range(self.table.rowCount()):
                row_data = {}
                for c in range(self.table.columnCount()):
                    col_name = self.table.horizontalHeaderItem(c).text()
                    widget = self.table.cellWidget(r, c)
                    if widget:
                        if isinstance(widget, QDateEdit):
                            row_data[col_name] = widget.date().toString("yyyy-MM-dd")
                        elif isinstance(widget, QComboBox):
                            row_data[col_name] = widget.currentText()
                    else:
                        item = self.table.item(r, c)
                        row_data[col_name] = item.text() if item else ""
                all_rows_data.append(row_data)

            new_df = pd.DataFrame(all_rows_data, columns=self.df.columns)
            
            # Validate the entire DataFrame
            is_valid, message = self.validation_func(new_df)
            if not is_valid:
                QMessageBox.warning(self, "Validation Error", message)
                return

            self.updated_df = new_df
            self.accept()

        except Exception as e:
            QMessageBox.critical(self, "Error Saving", f"An error occurred while preparing the data:\n{e}\n{traceback.format_exc()}")
            
    def get_updated_df(self):
        return self.updated_df


class ReportsSummaryDialog(QDialog):
    def __init__(self, parent=None, config=None, config_save_callback=None):
        super().__init__(parent)
        self.setWindowTitle("Reports Summary Dashboard")
        self.setMinimumSize(900, 800)

        # Config persistence
        self.app_config = config or {}
        self.config_save_callback = config_save_callback
        
        self.summary_handler = SummaryDataHandler(os.path.join(os.getcwd(), SUMMARY_DATA_FILE))
        self.integral_db = pd.DataFrame()
        self.mech_test_col = None

        self._setup_ui()
        self.refresh_dashboard()

    def _setup_ui(self):
        self.main_layout = QVBoxLayout(self)

        # --- Input Lists Management ---
        input_group = QGroupBox("Input Lists Management")
        input_layout = QGridLayout(input_group)
        self.lbl_qc = QLabel()
        self.btn_qc = QPushButton("Edit...")
        self.btn_qc.clicked.connect(self.manage_qc_list)
        self.lbl_qs = QLabel()
        self.btn_qs = QPushButton("Edit...")
        self.btn_qs.clicked.connect(self.manage_qs_list)
        self.lbl_ut = QLabel()
        self.btn_ut = QPushButton("Edit...")
        self.btn_ut.clicked.connect(self.manage_ut_list)
        self.lbl_misc = QLabel()
        self.btn_misc = QPushButton("Edit...")
        self.btn_misc.clicked.connect(self.manage_misc_list)

        input_layout.addWidget(QLabel("<b>QC OK List:</b>"), 0, 0); input_layout.addWidget(self.lbl_qc, 0, 1); input_layout.addWidget(self.btn_qc, 0, 2)
        input_layout.addWidget(QLabel("<b>QS OK List:</b>"), 1, 0); input_layout.addWidget(self.lbl_qs, 1, 1); input_layout.addWidget(self.btn_qs, 1, 2)
        input_layout.addWidget(QLabel("<b>UT Available List:</b>"), 2, 0); input_layout.addWidget(self.lbl_ut, 2, 1); input_layout.addWidget(self.btn_ut, 2, 2)
        input_layout.addWidget(QLabel("<b>Miscellaneous Tubes:</b>"), 3, 0); input_layout.addWidget(self.lbl_misc, 3, 1); input_layout.addWidget(self.btn_misc, 3, 2)
        input_layout.setColumnStretch(1, 1)
        self.main_layout.addWidget(input_group)

        # --- NCR Management ---
        ncr_group = QGroupBox("NCR Management")
        ncr_layout = QHBoxLayout(ncr_group)
        self.ncr_list_widget = QListWidget()
        self.ncr_list_widget.doubleClicked.connect(self.edit_ncr)
        ncr_layout.addWidget(self.ncr_list_widget, 2)
        
        ncr_button_layout = QVBoxLayout()
        self.btn_add_ncr = QPushButton("Add NCR")
        self.btn_add_ncr.clicked.connect(self.add_ncr)
        self.btn_edit_ncr = QPushButton("Edit Selected NCR")
        self.btn_edit_ncr.clicked.connect(self.edit_ncr)
        self.btn_delete_ncr = QPushButton("Delete Selected NCR")
        self.btn_delete_ncr.clicked.connect(self.delete_ncr)
        ncr_button_layout.addWidget(self.btn_add_ncr)
        ncr_button_layout.addWidget(self.btn_edit_ncr)
        ncr_button_layout.addWidget(self.btn_delete_ncr)
        ncr_button_layout.addStretch()
        ncr_layout.addLayout(ncr_button_layout, 1)
        self.main_layout.addWidget(ncr_group)

        # --- Dashboard ---
        dashboard_group = QGroupBox("Dashboard")
        dashboard_layout = QVBoxLayout(dashboard_group)

        product_col_layout = QHBoxLayout()
        product_col_layout.addWidget(QLabel("<b>Product Data Column:</b>"))
        self.combo_product_col = QComboBox()
        self.combo_product_col.setToolTip("Select the column from the database that represents 'Product' data.")
        self.combo_product_col.currentTextChanged.connect(self.calculate_all_metrics)
        product_col_layout.addWidget(self.combo_product_col, 1)
        dashboard_layout.addLayout(product_col_layout)

        metrics_layout = QGridLayout()
        self.metric_buttons = {
            'qc_ok': QPushButton("QC ok tubes:"),
            'qs_ok': QPushButton("QS ok tubes:"),
            'ev_avail': QPushButton("'EV reports available':"),
            'mech_pend': QPushButton("Mechanical Pending:"),
            'prod_pend': QPushButton("Product Pending:"),
            'either_pend': QPushButton("Either Mech or Product Pending:"),
            'ut_not_avail': QPushButton("UT not available:"),
            'tubes_for_ncr': QPushButton("Tubes for NCR:"),
        }
        for i, (key, btn) in enumerate(self.metric_buttons.items()):
            btn.setFont(QFont("Segoe UI", 10))
            btn.clicked.connect(lambda checked, k=key: self.download_metric_list(k))
            metrics_layout.addWidget(btn, i, 0)
        dashboard_layout.addLayout(metrics_layout)
        self.main_layout.addWidget(dashboard_group)
        
        # --- Actions ---
        action_layout = QHBoxLayout()
        self.btn_refresh = QPushButton("Refresh Dashboard")
        self.btn_refresh.clicked.connect(self.refresh_dashboard)
        action_layout.addWidget(self.btn_refresh)
        
        self.btn_download_all = QPushButton("Download Entire Data")
        self.btn_download_all.clicked.connect(self.download_entire_data)
        action_layout.addWidget(self.btn_download_all)
        action_layout.addStretch()
        
        self.btn_close = QPushButton("Close")
        self.btn_close.clicked.connect(self.accept)
        action_layout.addWidget(self.btn_close)
        self.main_layout.addLayout(action_layout)

    def refresh_dashboard(self):
        db_path = os.path.join(os.getcwd(), INTEGRAL_DATABASE_FILE)
        if os.path.exists(db_path):
            try:
                self.integral_db = pd.read_excel(db_path, dtype=str).fillna('')
                if TUBE_NUMBER_COL not in self.integral_db.columns:
                     QMessageBox.warning(self, "Database Error", f"Database must contain a '{TUBE_NUMBER_COL}' column for tube numbers.")
                     self.integral_db = pd.DataFrame()
                if MECHANICAL_TEST_COL_PRIMARY in self.integral_db.columns:
                    self.mech_test_col = MECHANICAL_TEST_COL_PRIMARY
                elif MECHANICAL_TEST_COL_FALLBACK in self.integral_db.columns:
                    self.mech_test_col = MECHANICAL_TEST_COL_FALLBACK
                else:
                    self.mech_test_col = None
            except Exception as e:
                QMessageBox.critical(self, "Database Load Error", f"Failed to load '{db_path}':\n{e}")
                self.integral_db = pd.DataFrame()
        else:
            self.integral_db = pd.DataFrame()

        self.summary_handler.load_data()
        self.qc_ok = self.summary_handler.get_sheet('QC_OK')
        self.qs_ok = self.summary_handler.get_sheet('QS_OK')
        self.ut_avail = self.summary_handler.get_sheet('UT_Available')
        self.misc = self.summary_handler.get_sheet('Miscellaneous')

        self.lbl_qc.setText(f"{len(self.qc_ok)} tubes")
        self.lbl_qs.setText(f"{len(self.qs_ok)} tubes")
        self.lbl_ut.setText(f"{len(self.ut_avail)} tubes")
        self.lbl_misc.setText(f"{len(self.misc)} tubes")
        
        self._update_ncr_list_widget()

        current_product_col = self.combo_product_col.currentText()
        self.combo_product_col.blockSignals(True)
        self.combo_product_col.clear()
        if not self.integral_db.empty:
            db_cols = self.integral_db.columns.tolist()
            self.combo_product_col.addItems(db_cols)
            saved_col = self.app_config.get('product_column')
            if saved_col in db_cols: self.combo_product_col.setCurrentText(saved_col)
            elif current_product_col in db_cols: self.combo_product_col.setCurrentText(current_product_col)
        self.combo_product_col.blockSignals(False)

        self.calculate_all_metrics()

    def calculate_all_metrics(self, _=None):
        # --- 1. INITIAL SETUP ---
        qc_ok_filtered = self.qc_ok[self.qc_ok['Tube No.'].astype(str).str.strip() != '']
        qc_tubes = set(qc_ok_filtered['Tube No.'].unique())
        self.metric_data = {}

        self.metric_data['qc_ok'] = qc_ok_filtered
        self.metric_buttons['qc_ok'].setText(f"QC ok tubes: {len(qc_tubes)}")
        
        qs_ok_filtered = self.qs_ok[self.qs_ok['Tube No.'].astype(str).str.strip() != '']
        qs_ok_tubes = set(qs_ok_filtered['Tube No.'].unique())
        self.metric_data['qs_ok'] = qs_ok_filtered
        self.metric_buttons['qs_ok'].setText(f"QS ok tubes: {len(qs_ok_filtered)}")

        db_for_qc_tubes = pd.DataFrame()
        if not self.integral_db.empty and TUBE_NUMBER_COL in self.integral_db.columns:
            db_for_qc_tubes = self.integral_db[self.integral_db[TUBE_NUMBER_COL].isin(qc_tubes)].copy()

        # --- 2. CALCULATE METRICS ---
        ev_avail_tubes = set()
        if not db_for_qc_tubes.empty:
            data_cols = [c for c in self.integral_db.columns if c not in ['Source_Folder', 'Source_File', TUBE_NUMBER_COL]]
            complete_records_df = db_for_qc_tubes.copy()
            for col in data_cols:
                if col in complete_records_df.columns:
                    complete_records_df.dropna(subset=[col], inplace=True)
                    complete_records_df = complete_records_df[complete_records_df[col].astype(str).str.strip() != '']
            if not complete_records_df.empty:
                ev_avail_tubes = set(complete_records_df[TUBE_NUMBER_COL].unique())
            self.metric_data['ev_avail'] = complete_records_df
        else:
            self.metric_data['ev_avail'] = pd.DataFrame()
        self.metric_buttons['ev_avail'].setText(f"'EV reports available': {len(ev_avail_tubes)}")

        mech_present_tubes = set()
        if not db_for_qc_tubes.empty and self.mech_test_col and self.mech_test_col in db_for_qc_tubes.columns:
            mech_valid_records = db_for_qc_tubes.dropna(subset=[self.mech_test_col])
            mech_valid_records = mech_valid_records[mech_valid_records[self.mech_test_col].astype(str).str.strip() != '']
            if not mech_valid_records.empty:
                mech_present_tubes = set(mech_valid_records[TUBE_NUMBER_COL].unique())
        mech_pending_tubes_set = qc_tubes - mech_present_tubes
        self.metric_data['mech_pend'] = pd.DataFrame(list(mech_pending_tubes_set), columns=[TUBE_NUMBER_COL])
        self.metric_buttons['mech_pend'].setText(f"Mechanical Pending: {len(mech_pending_tubes_set)}")

        prod_present_tubes = set()
        product_col = self.combo_product_col.currentText()
        if not db_for_qc_tubes.empty and product_col and product_col in db_for_qc_tubes.columns:
            prod_valid_records = db_for_qc_tubes.dropna(subset=[product_col])
            prod_valid_records = prod_valid_records[prod_valid_records[product_col].astype(str).str.strip() != '']
            if not prod_valid_records.empty:
                prod_present_tubes = set(prod_valid_records[TUBE_NUMBER_COL].unique())
        prod_pending_tubes_set = qc_tubes - prod_present_tubes
        self.metric_data['prod_pend'] = pd.DataFrame(list(prod_pending_tubes_set), columns=[TUBE_NUMBER_COL])
        self.metric_buttons['prod_pend'].setText(f"Product Pending: {len(prod_pending_tubes_set)}")
        
        either_pending_tubes_set = qc_tubes - ev_avail_tubes
        self.metric_data['either_pend'] = pd.DataFrame(list(either_pending_tubes_set), columns=[TUBE_NUMBER_COL])
        self.metric_buttons['either_pend'].setText(f"Either Mech or Product Pending: {len(either_pending_tubes_set)}")

        ut_avail_filtered = self.ut_avail[self.ut_avail['Tube No.'].astype(str).str.strip() != '']
        ut_avail_tubes = set(ut_avail_filtered['Tube No.'].unique())
        ut_not_avail_tubes = qc_tubes - ut_avail_tubes
        self.metric_data['ut_not_avail'] = pd.DataFrame(list(ut_not_avail_tubes), columns=[TUBE_NUMBER_COL])
        self.metric_buttons['ut_not_avail'].setText(f"UT not available: {len(ut_not_avail_tubes)}")

        all_ncr_tubes = set()
        for sheet_name, df in self.summary_handler.sheets.items():
            if sheet_name.startswith('NCR-') and 'Tube No.' in df.columns:
                ncr_tubes_in_sheet = df['Tube No.'].dropna().astype(str).str.strip()
                all_ncr_tubes.update(ncr_tubes_in_sheet[ncr_tubes_in_sheet != ''].unique())
        base_set_for_ncr = qs_ok_tubes.intersection(ev_avail_tubes)
        tubes_for_ncr_set = base_set_for_ncr - all_ncr_tubes
        self.metric_data['tubes_for_ncr'] = pd.DataFrame(list(tubes_for_ncr_set), columns=['Tube No.'])
        self.metric_buttons['tubes_for_ncr'].setText(f"Tubes for NCR: {len(tubes_for_ncr_set)}")

    def download_metric_list(self, key):
        df_to_download = self.metric_data.get(key)
        if df_to_download is None or df_to_download.empty:
            QMessageBox.information(self, "No Data", "There are no tubes in this category to download.")
            return
        tube_col = TUBE_NUMBER_COL if TUBE_NUMBER_COL in df_to_download.columns else 'Tube No.'
        if tube_col not in df_to_download.columns:
             tube_col = 'Tube nos.' if 'Tube nos.' in df_to_download.columns else None
        if not tube_col:
             QMessageBox.warning(self, "Error", f"Could not find a tube number column in the data for '{key}'.")
             return
        download_df = df_to_download[[tube_col]].drop_duplicates().reset_index(drop=True)
        download_df.columns = ['Tube Number']
        default_filename = f"summary_{key}_list.xlsx"
        path, _ = QFileDialog.getSaveFileName(self, "Save List", default_filename, "Excel Files (*.xlsx)")
        if not path: return
        try:
            download_df.to_excel(path, index=False)
            QMessageBox.information(self, "Success", f"List saved successfully to:\n{path}")
        except Exception as e:
            QMessageBox.critical(self, "Save Error", f"Failed to save the list:\n{e}")
            
    def download_entire_data(self):
        # ... (This method is unchanged and correct)
        pass # Placeholder for brevity, the original correct code goes here

    # --- LIST MANAGEMENT AND VALIDATION ---

    def _manage_list(self, title, df, column_configs, validation_func, sheet_name):
        dlg = FlexibleListEditorDialog(self, df, title, column_configs, validation_func)
        if dlg.exec_() and dlg.updated_df is not None:
            self.summary_handler.update_sheet(sheet_name, dlg.get_updated_df())
            self.refresh_dashboard()

    def manage_qc_list(self):
        configs = {'Sr no.': {'type': 'readonly'}, 'Tube No.': {}, 'QC Done Date': {'type': 'date'}}
        self._manage_list("QC OK Tubes", self.qc_ok, configs, self._validate_qc_list, 'QC_OK')

    def _validate_qc_list(self, df_to_validate):
        validator_handler = SummaryDataHandler(os.path.join(os.getcwd(), SUMMARY_DATA_FILE))
        
        df_to_validate['Tube No.'] = df_to_validate['Tube No.'].str.strip()
        if (df_to_validate['Tube No.'] == '').any(): return False, "Tube Number cannot be empty."
        if df_to_validate['Tube No.'].duplicated().any(): return False, "Duplicate Tube Numbers found in the QC OK list."
        
        misc_df = validator_handler.get_sheet('Miscellaneous')
        misc_tubes = set(misc_df['Tube nos.'].str.strip().unique())
        current_qc_tubes = set(df_to_validate['Tube No.'].unique())
        clash_tubes = current_qc_tubes.intersection(misc_tubes)
        if clash_tubes:
            return False, f"These tubes exist in the Miscellaneous list and cannot be in QC OK: {', '.join(sorted(list(clash_tubes)))}"
        return True, ""

    def manage_qs_list(self):
        configs = {'Sr no.': {'type': 'readonly'}, 'Tube No.': {}, 'QS Done Date': {'type': 'date'}}
        self._manage_list("QS OK Tubes", self.qs_ok, configs, self._validate_qs_list, 'QS_OK')

    def _validate_qs_list(self, df_to_validate):
        validator_handler = SummaryDataHandler(os.path.join(os.getcwd(), SUMMARY_DATA_FILE))

        df_to_validate['Tube No.'] = df_to_validate['Tube No.'].str.strip()
        if (df_to_validate['Tube No.'] == '').any(): return False, "Tube Number cannot be empty."
        if df_to_validate['Tube No.'].duplicated().any(): return False, "Duplicate Tube Numbers found in the QS OK list."
        
        qc_ok_df = validator_handler.get_sheet('QC_OK')
        qc_tubes = set(qc_ok_df['Tube No.'].str.strip().unique())
        qs_tubes = set(df_to_validate['Tube No.'].unique())
        invalid_tubes = qs_tubes - qc_tubes
        if invalid_tubes: return False, f"These tubes are not in the QC OK list: {', '.join(sorted(list(invalid_tubes)))}"
        return True, ""

    def manage_ut_list(self):
        configs = {'Sr no.': {'type': 'readonly'}, 'Tube No.': {}}
        self._manage_list("UT Available Tubes", self.ut_avail, configs, self._validate_ut_list, 'UT_Available')

    def _validate_ut_list(self, df_to_validate):
        df_to_validate['Tube No.'] = df_to_validate['Tube No.'].str.strip()
        if (df_to_validate['Tube No.'] == '').any(): return False, "Tube Number cannot be empty."
        if df_to_validate['Tube No.'].duplicated().any(): return False, "Duplicate Tube Numbers found in the UT Available list."
        return True, ""

    def manage_misc_list(self):
        configs = {'S No.': {'type': 'readonly'}, 'Tube nos.': {}, 'Date': {'type': 'date'},
                   'Status': {'type': 'combo', 'options': ['Reject', 'Hold']}, 'Remarks': {}}
        self._manage_list("Miscellaneous Tubes", self.misc, configs, self._validate_misc_list, 'Miscellaneous')
        
    def _validate_misc_list(self, df_to_validate):
        validator_handler = SummaryDataHandler(os.path.join(os.getcwd(), SUMMARY_DATA_FILE))

        df_to_validate['Tube nos.'] = df_to_validate['Tube nos.'].str.strip()
        if (df_to_validate['Tube nos.'] == '').any(): return False, "Tube Number cannot be empty."
        if df_to_validate['Tube nos.'].duplicated().any(): return False, "Duplicate Tube Numbers found in the Miscellaneous list."
        
        qc_ok_df = validator_handler.get_sheet('QC_OK')
        qc_tubes = set(qc_ok_df['Tube No.'].str.strip().unique())
        misc_tubes = set(df_to_validate['Tube nos.'].unique())
        clash_tubes = misc_tubes.intersection(qc_tubes)
        if clash_tubes: return False, f"These tubes exist in the QC OK list and cannot be in Miscellaneous: {', '.join(sorted(list(clash_tubes)))}"
        return True, ""

    # --- NCR Management Methods ---
    def _update_ncr_list_widget(self):
        self.ncr_list_widget.clear()
        ncr_sheets = sorted([name for name in self.summary_handler.sheets.keys() if name.startswith('NCR-')])
        self.ncr_list_widget.addItems(ncr_sheets)
        
    def add_ncr(self):
        ncr_numbers = [int(name.split('-')[1]) for name in self.summary_handler.sheets.keys() if name.startswith('NCR-') and name.split('-')[1].isdigit()]
        next_ncr_num = max(ncr_numbers) + 1 if ncr_numbers else 1
        new_ncr_name = f"NCR-{next_ncr_num}"
        
        new_df = pd.DataFrame(columns=['Sr no.', 'Tube No.'])
        self.summary_handler.update_sheet(new_ncr_name, new_df)
        self.refresh_dashboard()
        items = self.ncr_list_widget.findItems(new_ncr_name, Qt.MatchExactly)
        if items:
            self.ncr_list_widget.setCurrentItem(items[0])
            self.edit_ncr()

    def edit_ncr(self):
        selected_item = self.ncr_list_widget.currentItem()
        if not selected_item:
            QMessageBox.information(self, "No Selection", "Please select an NCR from the list to edit.")
            return
        
        ncr_name = selected_item.text()
        ncr_df = self.summary_handler.get_sheet(ncr_name)
        configs = {'Sr no.': {'type': 'readonly'}, 'Tube No.': {}}
        
        validation_func = lambda df: self._validate_ncr_list(df, ncr_name)
        self._manage_list(ncr_name, ncr_df, configs, validation_func, ncr_name)

    def _validate_ncr_list(self, df_to_validate, current_ncr_name):
        # ** THE FIX **: Create a new handler to read the latest state from disk for validation.
        validator_handler = SummaryDataHandler(os.path.join(os.getcwd(), SUMMARY_DATA_FILE))

        df_to_validate['Tube No.'] = df_to_validate['Tube No.'].str.strip()
        if (df_to_validate['Tube No.'] == '').any():
            return False, "Tube Number cannot be empty."
        
        # Rule 2: A tube cannot be duplicated within this specific NCR list
        if df_to_validate['Tube No.'].duplicated().any():
            return False, f"Duplicate Tube Numbers found within this list ({current_ncr_name})."

        current_ncr_tubes = set(df_to_validate[df_to_validate['Tube No.'].str.strip() != '']['Tube No.'].unique())
        
        # Rule a: A tube must be in the QS OK list to be in any NCR list
        # Use the validator_handler to get the freshest data.
        qs_ok_df = validator_handler.get_sheet('QS_OK')
        qs_ok_tubes = set(qs_ok_df['Tube No.'].str.strip().unique())
        invalid_tubes = current_ncr_tubes - qs_ok_tubes
        if invalid_tubes:
            return False, f"The following tubes are not in the 'QS OK' list and cannot be added to an NCR:\n\n{', '.join(sorted(list(invalid_tubes)))}"

        # Rule 2 (re-checked against all other NCRs): A tube cannot be in multiple NCR lists
        all_other_ncr_tubes = set()
        # Use the validator_handler to get the freshest data.
        for sheet_name, df in validator_handler.sheets.items():
            if sheet_name.startswith('NCR-') and sheet_name != current_ncr_name and 'Tube No.' in df.columns:
                other_ncr_tubes = df['Tube No.'].dropna().astype(str).str.strip()
                all_other_ncr_tubes.update(other_ncr_tubes[other_ncr_tubes != ''].unique())
        clash_tubes = current_ncr_tubes.intersection(all_other_ncr_tubes)
        if clash_tubes:
            return False, f"The following tubes already exist in other NCR lists and cannot be added again:\n\n{', '.join(sorted(list(clash_tubes)))}"

        return True, ""

    def delete_ncr(self):
        selected_item = self.ncr_list_widget.currentItem()
        if not selected_item:
            QMessageBox.information(self, "No Selection", "Please select an NCR from the list to delete.")
            return

        ncr_name = selected_item.text()
        reply = QMessageBox.question(self, 'Confirm Deletion', 
                                     f"Are you sure you want to permanently delete '{ncr_name}' and all its tube numbers?",
                                     QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            self.summary_handler.delete_sheet(ncr_name)
            self.refresh_dashboard()

    def closeEvent(self, event):
        selected_product_col = self.combo_product_col.currentText()
        if selected_product_col:
            self.app_config['product_column'] = selected_product_col
            if self.config_save_callback:
                self.config_save_callback()
        super().closeEvent(event)

if __name__ == '__main__':
    app = QApplication(sys.argv)
    dialog = ReportsSummaryDialog()
    sys.exit(dialog.exec_())
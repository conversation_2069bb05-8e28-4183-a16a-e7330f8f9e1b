#!/usr/bin/env python3
"""
Test script for responsive design across different screen sizes
Tests the Report Maker v2 responsive UI at various breakpoints
"""

import sys
import os
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton, 
    QHBoxLayout, QComboBox, QTextEdit, QGroupBox, QSplitter
)
from PyQt5.QtCore import Qt, QTimer, QSize
from PyQt5.QtGui import QFont

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from responsive_ui import (
        ResponsiveWidget, ResponsiveSplitter, ScrollableContainer, 
        ExcelLikeTableWidget, apply_responsive_stylesheet
    )
    from enhanced_widgets import (
        EnhancedDataTable, ResponsiveFormWidget, EnhancedProgressWidget,
        ResponsiveFileSelector
    )
    print("✓ Responsive design components imported successfully")
    RESPONSIVE_AVAILABLE = True
except ImportError as e:
    print(f"✗ Failed to import responsive components: {e}")
    RESPONSIVE_AVAILABLE = False


class ResponsiveTestWindow(QMainWindow):
    """Test window for responsive design across screen sizes"""
    
    # Common screen size breakpoints
    SCREEN_SIZES = {
        "Mobile (320px)": (320, 568),
        "Mobile Large (375px)": (375, 667),
        "Tablet (768px)": (768, 1024),
        "Desktop (1024px)": (1024, 768),
        "Large Desktop (1440px)": (1440, 900),
        "Ultra Wide (1920px)": (1920, 1080)
    }
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Report Maker v2 - Responsive Design Test")
        self.setMinimumSize(320, 480)
        
        if RESPONSIVE_AVAILABLE:
            self.setStyleSheet(apply_responsive_stylesheet())
            self.setup_responsive_test()
        else:
            self.setup_fallback()
        
        # Start with desktop size
        self.resize(1024, 768)
    
    def setup_responsive_test(self):
        """Setup responsive design test"""
        # Central responsive widget
        central_widget = ResponsiveWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Control panel
        self.setup_control_panel(layout)
        
        # Main content area with responsive splitter
        self.setup_main_content(layout)
        
        # Status area
        self.setup_status_area(layout)
        
        # Connect resize events
        central_widget.resizeEvent = self.on_window_resize
        
        print("✓ Responsive design test setup complete")
    
    def setup_control_panel(self, parent_layout):
        """Setup control panel for testing different screen sizes"""
        control_group = QGroupBox("Responsive Design Test Controls")
        control_layout = QVBoxLayout(control_group)
        
        # Screen size selector
        size_layout = QHBoxLayout()
        size_layout.addWidget(QLabel("Test Screen Size:"))
        
        self.size_combo = QComboBox()
        self.size_combo.addItems(list(self.SCREEN_SIZES.keys()))
        self.size_combo.currentTextChanged.connect(self.change_screen_size)
        size_layout.addWidget(self.size_combo)
        
        self.btn_auto_test = QPushButton("Auto Test All Sizes")
        self.btn_auto_test.clicked.connect(self.auto_test_sizes)
        size_layout.addWidget(self.btn_auto_test)
        
        size_layout.addStretch()
        control_layout.addLayout(size_layout)
        
        # Current size display
        self.size_label = QLabel("Current: 1024x768 (Desktop)")
        self.size_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        control_layout.addWidget(self.size_label)
        
        parent_layout.addWidget(control_group)
    
    def setup_main_content(self, parent_layout):
        """Setup main content area with responsive components"""
        # Responsive splitter
        self.main_splitter = ResponsiveSplitter()
        parent_layout.addWidget(self.main_splitter)
        
        # Left panel - Form and controls
        left_panel = ResponsiveWidget()
        left_layout = QVBoxLayout(left_panel)
        
        # Responsive form
        form_group = QGroupBox("Responsive Form")
        form_layout = QVBoxLayout(form_group)
        
        self.form_widget = ResponsiveFormWidget()
        self.form_widget.add_form_field("Name", QLineEdit(), required=True)
        self.form_widget.add_form_field("Email", QLineEdit(), required=True)
        
        category_combo = QComboBox()
        category_combo.addItems(["Select...", "Option 1", "Option 2", "Option 3"])
        self.form_widget.add_form_field("Category", category_combo)
        
        form_layout.addWidget(self.form_widget)
        left_layout.addWidget(form_group)
        
        # File selector
        file_group = QGroupBox("File Selection")
        file_layout = QVBoxLayout(file_group)
        
        self.file_selector = ResponsiveFileSelector()
        file_layout.addWidget(self.file_selector)
        left_layout.addWidget(file_group)
        
        # Progress widget
        progress_group = QGroupBox("Progress")
        progress_layout = QVBoxLayout(progress_group)
        
        self.progress_widget = EnhancedProgressWidget()
        self.progress_widget.set_progress(45, 100, "Processing...", "Testing responsive progress")
        progress_layout.addWidget(self.progress_widget)
        left_layout.addWidget(progress_group)
        
        self.main_splitter.addWidget(left_panel)
        
        # Right panel - Data table
        right_panel = ResponsiveWidget()
        right_layout = QVBoxLayout(right_panel)
        
        table_group = QGroupBox("Responsive Data Table")
        table_layout = QVBoxLayout(table_group)
        
        self.data_table = EnhancedDataTable()
        self.data_table.setRowCount(8)
        self.data_table.setColumnCount(5)
        self.data_table.setHorizontalHeaderLabels(["Name", "Value", "Category", "Date", "Status"])
        
        # Add sample data
        sample_data = [
            ["Item 1", "100", "Type A", "2024-01-01", "Active"],
            ["Item 2", "200", "Type B", "2024-01-02", "Pending"],
            ["Item 3", "300", "Type A", "2024-01-03", "Complete"],
        ]
        
        for row, row_data in enumerate(sample_data):
            for col, cell_data in enumerate(row_data):
                from PyQt5.QtWidgets import QTableWidgetItem
                item = QTableWidgetItem(str(cell_data))
                self.data_table.setItem(row, col, item)
        
        table_layout.addWidget(self.data_table)
        right_layout.addWidget(table_group)
        
        self.main_splitter.addWidget(right_panel)
        
        # Set initial splitter sizes
        self.main_splitter.setSizes([400, 600])
    
    def setup_status_area(self, parent_layout):
        """Setup status area"""
        status_group = QGroupBox("Responsive Behavior Status")
        status_layout = QVBoxLayout(status_group)
        
        self.status_text = QTextEdit()
        self.status_text.setMaximumHeight(120)
        self.status_text.setPlaceholderText("Responsive behavior information will appear here...")
        status_layout.addWidget(self.status_text)
        
        parent_layout.addWidget(status_group)
    
    def setup_fallback(self):
        """Setup fallback when responsive features not available"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        label = QLabel("Responsive design features not available")
        label.setAlignment(Qt.AlignCenter)
        label.setStyleSheet("color: red; font-size: 16px; margin: 20px;")
        layout.addWidget(label)
    
    def change_screen_size(self, size_name):
        """Change window size to test different screen sizes"""
        if size_name in self.SCREEN_SIZES:
            width, height = self.SCREEN_SIZES[size_name]
            self.resize(width, height)
            self.update_size_display(width, height, size_name)
            self.log_responsive_behavior(size_name, width, height)
    
    def update_size_display(self, width, height, size_name):
        """Update size display label"""
        category = self.get_size_category(width)
        self.size_label.setText(f"Current: {width}x{height} ({category})")
    
    def get_size_category(self, width):
        """Get size category based on width"""
        if width < 768:
            return "Mobile"
        elif width < 1024:
            return "Tablet"
        elif width < 1920:
            return "Desktop"
        else:
            return "Large Desktop"
    
    def log_responsive_behavior(self, size_name, width, height):
        """Log responsive behavior changes"""
        category = self.get_size_category(width)
        
        behavior_info = f"=== {size_name} ({width}x{height}) ===\n"
        behavior_info += f"Category: {category}\n"
        
        # Check splitter orientation
        if hasattr(self, 'main_splitter'):
            orientation = "Horizontal" if self.main_splitter.orientation() == Qt.Horizontal else "Vertical"
            behavior_info += f"Splitter Orientation: {orientation}\n"
        
        # Check form layout
        if hasattr(self, 'form_widget'):
            behavior_info += "Form Layout: Responsive\n"
        
        # Check table behavior
        if hasattr(self, 'data_table'):
            behavior_info += f"Table Columns Visible: {self.data_table.columnCount()}\n"
        
        behavior_info += f"Expected Behavior:\n"
        if width < 768:
            behavior_info += "- Vertical layout for mobile\n- Stacked form fields\n- Simplified navigation\n"
        elif width < 1024:
            behavior_info += "- Tablet layout\n- Horizontal form with wrapping\n- Responsive table\n"
        else:
            behavior_info += "- Desktop layout\n- Horizontal splitter\n- Full table functionality\n"
        
        behavior_info += "\n"
        
        self.status_text.append(behavior_info)
    
    def auto_test_sizes(self):
        """Automatically test all screen sizes"""
        self.status_text.clear()
        self.status_text.append("=== AUTO TESTING ALL SCREEN SIZES ===\n")
        
        sizes = list(self.SCREEN_SIZES.keys())
        self.current_test_index = 0
        self.test_sizes = sizes
        
        # Start auto test timer
        self.auto_test_timer = QTimer()
        self.auto_test_timer.timeout.connect(self.next_auto_test)
        self.auto_test_timer.start(3000)  # 3 seconds per size
        
        # Start with first size
        self.next_auto_test()
    
    def next_auto_test(self):
        """Move to next size in auto test"""
        if self.current_test_index < len(self.test_sizes):
            size_name = self.test_sizes[self.current_test_index]
            self.size_combo.setCurrentText(size_name)
            self.current_test_index += 1
        else:
            # Auto test complete
            self.auto_test_timer.stop()
            self.status_text.append("=== AUTO TEST COMPLETE ===\n")
            self.status_text.append("All screen sizes tested. Review the behavior above.\n")
    
    def on_window_resize(self, event):
        """Handle window resize events"""
        if hasattr(self, 'size_label'):
            size = event.size()
            width, height = size.width(), size.height()
            category = self.get_size_category(width)
            self.size_label.setText(f"Current: {width}x{height} ({category})")
        
        # Call original resize event
        super().resizeEvent(event)


def main():
    """Main test function"""
    print("=== Report Maker v2 Responsive Design Test ===")
    
    app = QApplication.instance() or QApplication(sys.argv)
    
    window = ResponsiveTestWindow()
    window.show()
    
    print(f"Responsive Features Available: {RESPONSIVE_AVAILABLE}")
    
    if RESPONSIVE_AVAILABLE:
        print("\n=== Responsive Design Test Instructions ===")
        print("1. Use the dropdown to test different screen sizes")
        print("2. Click 'Auto Test All Sizes' to automatically cycle through sizes")
        print("3. Manually resize the window to test responsive behavior")
        print("4. Watch how components adapt to different screen sizes:")
        print("   - Splitter orientation changes")
        print("   - Form layout adapts")
        print("   - Table responsiveness")
        print("   - Component visibility and sizing")
        print("5. Check the status area for detailed behavior information")
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()

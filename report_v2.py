import sys
import os
import json
import hashlib
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
    QFileDialog, QLabel, QGraphicsView, QGraphicsScene, QGraphicsPixmapItem,
    QGraphicsRectItem, QInputDialog, QMessageBox, QListWidget, QListWidgetItem,
    QDialog, QLineEdit, QGraphicsSimpleTextItem, QGraphicsTextItem,

    QComboBox, QCheckBox, QTextEdit, QGroupBox, QSpinBox, QSlider,
    QDialogButtonBox, QSplitter, QStackedWidget, QGridLayout, QProgressBar,
    QTableWidget, QTableWidgetItem, QHeaderView, QAbstractItemView, QRadioButton,
    QProgressDialog, QSizePolicy, QScrollArea, QFrame
)

# Import responsive UI components
try:
    from responsive_ui import (
        ResponsiveWidget, ResponsiveLayout, ResponsiveSplitter, ScrollableContainer,
        ExcelLikeTableWidget, apply_responsive_stylesheet
    )
    from enhanced_widgets import (
        EnhancedDataTable, ResponsiveFormWidget, EnhancedProgressWidget,
        ResponsiveFileSelector
    )
    RESPONSIVE_UI_AVAILABLE = True
except ImportError as e:
    print(f"Responsive UI components not available: {e}")
    RESPONSIVE_UI_AVAILABLE = False
from PyQt5.QtGui import QPixmap, QPainter, QPen, QImage, QColor, QDrag
from PyQt5.QtCore import Qt, QRectF, QPointF, QMimeData, pyqtSignal

from pdf2image import convert_from_path
from PIL import Image, ImageEnhance, ImageFilter
from reports_summary import ReportsSummaryDialog

try:
    from skimage.filters import threshold_local
    from skimage.util import img_as_ubyte
    import numpy as np
    SCIKIT_AVAILABLE = True
except ImportError:
    SCIKIT_AVAILABLE = False

try:
    import pdfplumber
    PDFPLUMBER_AVAILABLE = True
except ImportError:
    PDFPLUMBER_AVAILABLE = False

try:
    import openpyxl
    OPENPYXL_AVAILABLE = True
except ImportError:
    OPENPYXL_AVAILABLE = False

import pytesseract
import pandas as pd
import re
import traceback
import shutil

# --- Configuration & Setup ---
PROFILES_DIR = "profiles"
CACHE_DIR = ".cache"
MERGE_CONFIGS_DIR = "merge_configs"
BULK_PROFILES_DIR = "bulk_report_profiles"
BULK_REPORTS_DIR = "report_outputs"
APP_CONFIG_FILE = "app_config.json"
INTEGRAL_DATABASE_FILE = "integral_database.xlsx"

if getattr(sys, 'frozen', False):
    # If the application is run as a bundle/frozen exe
    script_base_dir = os.path.dirname(sys.executable)
else:
    # If the application is run as a normal .py script
    try:
        script_base_dir = os.path.dirname(os.path.abspath(__file__))
    except NameError:
        # Fallback for environments where __file__ is not defined (e.g., interactive)
        script_base_dir = os.getcwd()

profiles_full_path = os.path.join(script_base_dir, PROFILES_DIR)
cache_full_path = os.path.join(script_base_dir, CACHE_DIR)
merge_configs_full_path = os.path.join(script_base_dir, MERGE_CONFIGS_DIR)
bulk_profiles_full_path = os.path.join(script_base_dir, BULK_PROFILES_DIR)
bulk_reports_full_path = os.path.join(script_base_dir, BULK_REPORTS_DIR)
integral_database_path = os.path.join(script_base_dir, INTEGRAL_DATABASE_FILE)
if not os.path.exists(profiles_full_path): os.makedirs(profiles_full_path)
if not os.path.exists(cache_full_path): os.makedirs(cache_full_path)
if not os.path.exists(merge_configs_full_path): os.makedirs(merge_configs_full_path)
if not os.path.exists(bulk_profiles_full_path): os.makedirs(bulk_profiles_full_path)
if not os.path.exists(bulk_reports_full_path): os.makedirs(bulk_reports_full_path)


tesseract_cmd_path = r".\Tesseract-OCR\tesseract.exe"
if tesseract_cmd_path and not os.path.isabs(tesseract_cmd_path):
    abs_tesseract_path = os.path.join(script_base_dir, tesseract_cmd_path)
    if os.path.exists(abs_tesseract_path):
        pytesseract.pytesseract.tesseract_cmd = abs_tesseract_path
elif tesseract_cmd_path and os.path.isabs(tesseract_cmd_path) and os.path.exists(tesseract_cmd_path):
    pytesseract.pytesseract.tesseract_cmd = tesseract_cmd_path

poppler_path = r".\poppler\Library\bin"
current_poppler_path_for_pdf2image = None
if sys.platform.startswith('win32') and poppler_path:
    def setup_external_path(env_var_path):
        abs_path = os.path.join(script_base_dir, env_var_path) if not os.path.isabs(env_var_path) else env_var_path
        potential_bin_paths = [abs_path, os.path.join(abs_path, "bin"), os.path.join(abs_path, "Library", "bin")]
        for p_bin_path in potential_bin_paths:
            norm_path = os.path.normpath(p_bin_path)
            if os.path.isdir(norm_path):
                if norm_path not in os.environ['PATH']:
                    os.environ['PATH'] += os.pathsep + norm_path
                return norm_path
        return None
    current_poppler_path_for_pdf2image = setup_external_path(poppler_path)


class Canvas(QGraphicsView):
    def __init__(self, scene, parent=None):
        super().__init__(scene, parent)
        self.start_point = QPointF()
        self.current_rect_item = None
        self.drawing = False
        self.parent_dialog = parent
        self.setRenderHint(QPainter.Antialiasing)
        self.setRenderHint(QPainter.SmoothPixmapTransform)
        self.setTransformationAnchor(QGraphicsView.AnchorUnderMouse)
        self.setResizeAnchor(QGraphicsView.AnchorUnderMouse)
        self.setDragMode(QGraphicsView.ScrollHandDrag)

    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton and hasattr(self.parent_dialog, 'is_calibration_mode') and self.parent_dialog.is_calibration_mode:
            self.setDragMode(QGraphicsView.NoDrag)
            self.start_point = self.mapToScene(event.pos())
            self.drawing = True
            if self.current_rect_item:
                self.scene().removeItem(self.current_rect_item)
            self.current_rect_item = QGraphicsRectItem(QRectF(self.start_point, self.start_point))
            self.current_rect_item.setPen(QPen(QColor("red"), 2, Qt.SolidLine))
            self.scene().addItem(self.current_rect_item)
        super().mousePressEvent(event)

    def mouseMoveEvent(self, event):
        if self.drawing and self.current_rect_item:
            self.current_rect_item.setRect(QRectF(self.start_point, self.mapToScene(event.pos())).normalized())
        super().mouseMoveEvent(event)

    def mouseReleaseEvent(self, event):
        if event.button() == Qt.LeftButton and self.drawing and self.current_rect_item:
            self.drawing = False
            self.setDragMode(QGraphicsView.ScrollHandDrag)
            if hasattr(self.parent_dialog, 'add_region_from_canvas'):
                self.parent_dialog.add_region_from_canvas(self.current_rect_item.rect())
        super().mouseReleaseEvent(event)

    def wheelEvent(self, event):
        zoom_factor = 1.25 if event.angleDelta().y() > 0 else 1 / 1.25
        self.setTransformationAnchor(QGraphicsView.AnchorUnderMouse)
        self.scale(zoom_factor, zoom_factor)


class DraggableListWidget(QListWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setDragEnabled(True)

    def startDrag(self, supportedActions):
        drag = QDrag(self)
        mime_data = QMimeData()
        item = self.currentItem()
        if item:
            mime_data.setText(item.text())
            drag.setMimeData(mime_data)
            drag.exec_(supportedActions)


class DroppableListWidget(QListWidget):
    item_linked = pyqtSignal(str, int)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setAcceptDrops(True)
        self.setDropIndicatorShown(True)

    def dragEnterEvent(self, event):
        if event.mimeData().hasText(): event.acceptProposedAction()
    def dragMoveEvent(self, event):
        if event.mimeData().hasText(): event.acceptProposedAction()

    def dropEvent(self, event):
        if event.mimeData().hasText():
            item = self.itemAt(event.pos())
            if item: self.item_linked.emit(event.mimeData().text(), self.row(item))


class DataLinkerDialog(QDialog):
    def __init__(self, link_targets, text, existing_config, is_row_linking=True, parent=None):
        super().__init__(parent)
        self.is_row_linking = is_row_linking
        self.setWindowTitle("Link Data to Row" if self.is_row_linking else "Link Data to Column")
        self.setMinimumSize(800, 500)
        self.layout = QVBoxLayout(self)
        self.text_to_process = text
        self.links = existing_config.get('links', {})
        self.chunking_group = QGroupBox("1. Select Text Splitting (Chunking) Method")
        chunking_layout = QGridLayout(self.chunking_group)
        self.combo_chunk_method = QComboBox()
        self.combo_chunk_method.addItems(["Smart Split", "Newline Delimited", "Delimiter", "Fixed Width", "Regex"])
        self.chunking_options_stack = QStackedWidget()
        self.param_smart = QLabel("Splits by multiple spaces or '|'. No options.")
        self.param_newline = QLabel("Splits text by each new line. Ideal for vertical data.")
        self.param_delimiter = QLineEdit()
        self.param_delimiter.setPlaceholderText("Enter delimiter character, e.g., ','")
        self.param_fixed = QSpinBox()
        self.param_fixed.setMinimum(1)
        self.param_regex = QLineEdit()
        self.param_regex.setPlaceholderText("Enter regex for splitting, e.g., '\\s+'")
        
        for w in [self.param_smart, self.param_newline, self.param_delimiter, self.param_fixed, self.param_regex]:
            self.chunking_options_stack.addWidget(w)
        
        chunking_layout.addWidget(QLabel("Method:"), 0, 0)
        chunking_layout.addWidget(self.combo_chunk_method, 0, 1)
        chunking_layout.addWidget(QLabel("Parameter:"), 1, 0)
        chunking_layout.addWidget(self.chunking_options_stack, 1, 1)
        self.layout.addWidget(self.chunking_group)
        
        target_label = "Columns" if self.is_row_linking else "Rows"
        linking_group = QGroupBox(f"2. Link {target_label} to Data Chunks")
        linking_layout = QVBoxLayout(linking_group)
        linking_layout.addWidget(QLabel(f"<b>Drag</b> a {target_label[:-1]} name from the left and <b>drop</b> it onto its data on the right."))
        
        self.splitter = QSplitter(Qt.Horizontal)
        self.target_list = DraggableListWidget()
        self.target_list.addItems(sorted(link_targets.keys()))
        self.data_list = DroppableListWidget()
        self.data_list.item_linked.connect(self.on_item_linked)
        self.splitter.addWidget(self.target_list)
        self.splitter.addWidget(self.data_list)
        linking_layout.addWidget(self.splitter)
        self.layout.addWidget(linking_group)

        self.button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        self.button_box.accepted.connect(self.accept)
        self.button_box.rejected.connect(self.reject)
        self.layout.addWidget(self.button_box)
        
        self.combo_chunk_method.currentIndexChanged.connect(self.update_chunker_ui)
        self.param_delimiter.textChanged.connect(self.rechunk_data)
        self.param_fixed.valueChanged.connect(self.rechunk_data)
        self.param_regex.textChanged.connect(self.rechunk_data)
        
        default_method = 'Newline Delimited' if not self.is_row_linking else 'Smart Split'
        self.combo_chunk_method.setCurrentText(existing_config.get('method', default_method))
        if self.combo_chunk_method.currentText() == "Delimiter":
            self.param_delimiter.setText(str(existing_config.get('param', '')))
        elif self.combo_chunk_method.currentText() == "Fixed Width":
            self.param_fixed.setValue(int(existing_config.get('param', 1)))
        elif self.combo_chunk_method.currentText() == "Regex":
            self.param_regex.setText(str(existing_config.get('param', '')))
        self.update_chunker_ui()

    def update_chunker_ui(self):
        method_map = {
            "Smart Split": self.param_smart,
            "Newline Delimited": self.param_newline,
            "Delimiter": self.param_delimiter,
            "Fixed Width": self.param_fixed,
            "Regex": self.param_regex
        }
        self.chunking_options_stack.setCurrentWidget(method_map.get(self.combo_chunk_method.currentText()))
        self.rechunk_data()

    def rechunk_data(self):
        method, text, chunks = self.combo_chunk_method.currentText(), self.text_to_process, []
        try:
            if method == "Smart Split":
                self.chunking_param = ''
                chunks = [val for val in re.split(r'\s{2,}|[|]', text) if val.strip()]
            elif method == "Newline Delimited":
                self.chunking_param = ''
                chunks = [val for val in text.split('\n') if val.strip()]
            elif method == "Delimiter":
                delimiter = self.param_delimiter.text()
                self.chunking_param = delimiter
                chunks = text.split(delimiter) if delimiter else []
            elif method == "Fixed Width":
                width = self.param_fixed.value()
                self.chunking_param = width
                chunks = [text[i:i + width] for i in range(0, len(text), width)] if width > 0 else []
            elif method == "Regex":
                pattern = self.param_regex.text()
                self.chunking_param = pattern
                chunks = re.split(pattern, text) if pattern else []
        except Exception as e:
            chunks = [f"ERROR: {e}"]
        self.chunked_data = [c.strip() for c in chunks if c.strip()]
        self.update_data_list_display()

    def on_item_linked(self, target_key, data_index):
        if target_key not in self.links: self.links[target_key] = []
        if data_index not in self.links[target_key]:
            self.links[target_key].append(data_index)
            self.links[target_key].sort()
        self.update_data_list_display()

    def update_data_list_display(self):
        self.data_list.clear()
        for i, chunk in enumerate(self.chunked_data):
            linked_targets = [col for col, indices in self.links.items() if i in indices]
            display_text = f"Chunk {i}: '{chunk}'"
            if linked_targets:
                display_text += f" -> LINKED TO: {', '.join(linked_targets)}"
            self.data_list.addItem(display_text)

    def get_config(self):
        return {'method': self.combo_chunk_method.currentText(), 'param': self.chunking_param, 'links': self.links}


class CalibrationDialog(QDialog):
    def __init__(self, parent=None, profile_path=None):
        super().__init__(parent)
        self.setWindowTitle("Calibration Editor - Origin-Based")
        self.setMinimumSize(1200, 900)
        self.loaded_profile_path = profile_path
        self.original_pil_image=None
        self.processed_pil_image=None
        self.current_pdf_path = None
        self.current_pixmap_item=None
        self.current_image_dims=None
        self.regions={}
        self.is_calibration_mode=False
        self.psm_config={"row_col_headers_psm":"7", "data_blocks_psm":"6"}
        self.enhancement_config={"to_greyscale":True,"contrast":1.0,"brightness":1.0,"apply_median":False,"apply_sharpen":False,"threshold_method":"adaptive","global_threshold":180,"adaptive_block_size":55,"adaptive_offset":10}
        
        self.layout=QVBoxLayout(self)
        self.top_instruction=QLabel("<b><u>Calibration Guide:</u></b><br>1. <b>Load Sample PDF</b>. If loading an existing profile, you will be prompted to do this automatically.<br>2. Configure Extraction Method and optional Sub-row usage.<br>3. <b>CRITICAL:</b> Draw and name a unique, small, and stable piece of text as the <code>origin</code>.<br>4. Define all other regions (e.g., <code>col_header_1</code>, <code>row_header_A</code>) by drawing boxes.<br>5. Use the <b>Link</b> buttons and <b>Test Extraction</b> button to build and verify your profile.")
        self.top_instruction.setWordWrap(True)
        self.layout.addWidget(self.top_instruction)

        main_split_layout=QHBoxLayout()
        left_panel=QWidget()
        left_panel_layout=QVBoxLayout(left_panel)
        self.right_panel = QWidget()
        right_panel_layout=QVBoxLayout(self.right_panel)
        self.right_panel.setLayout(right_panel_layout)
        main_split_layout.addWidget(left_panel, 2)
        main_split_layout.addWidget(self.right_panel, 1)
        self.layout.addLayout(main_split_layout)

        self._setup_left_panel(left_panel_layout)
        self._setup_enhancement_panel(right_panel_layout)
        self._setup_psm_and_regions_panel(right_panel_layout)
        self._update_ui_from_config()
        if self.loaded_profile_path: self.load_profile()

    def _setup_left_panel(self, layout):
        controls_layout=QHBoxLayout()
        self.btn_load=QPushButton("Load Sample PDF")
        self.btn_load.clicked.connect(self.load_pdf)
        controls_layout.addWidget(self.btn_load)
        self.name_input=QLineEdit()
        self.name_input.setPlaceholderText("Enter Field Name BEFORE Drawing")
        controls_layout.addWidget(self.name_input)
        layout.addLayout(controls_layout)
        self.scene=QGraphicsScene(self)
        self.canvas=Canvas(self.scene, self)
        layout.addWidget(self.canvas)
        
    def _setup_enhancement_panel(self, layout):
        self.enhancement_group=QGroupBox("Image Enhancement (for OCR)")
        group_layout=QVBoxLayout(self.enhancement_group)
        self.cb_grey=QCheckBox("Greyscale")
        group_layout.addWidget(self.cb_grey)
        
        def add_slider(name, min_val, max_val, default_val):
            h_layout = QHBoxLayout()
            h_layout.addWidget(QLabel(f"{name}:"))
            slider = QSlider(Qt.Horizontal)
            slider.setRange(min_val, max_val)
            slider.setValue(default_val)
            h_layout.addWidget(slider)
            return h_layout, slider
            
        cl,self.s_con=add_slider("Contrast",50,200,100)
        group_layout.addLayout(cl)
        bl,self.s_br=add_slider("Brightness",50,200,100)
        group_layout.addLayout(bl)
        
        reset=QPushButton("Reset Adjustments")
        reset.clicked.connect(self._reset_adjustments)
        group_layout.addWidget(reset)
        
        fg=QGroupBox("Filters")
        fl=QVBoxLayout(fg)
        self.cb_med=QCheckBox("Median Filter")
        self.cb_sh=QCheckBox("Sharpen Filter")
        fl.addWidget(self.cb_med)
        fl.addWidget(self.cb_sh)
        group_layout.addWidget(fg)
        
        tg=QGroupBox("Binarization")
        tl=QVBoxLayout(tg)
        self.combo_tm=QComboBox()
        tl.addWidget(self.combo_tm)
        if SCIKIT_AVAILABLE:
            self.combo_tm.addItems(["adaptive", "global"])
        else:
            self.combo_tm.addItems(["global"])
            self.combo_tm.setEnabled(False)
            
        self.gtw=QWidget()
        gl=QHBoxLayout(self.gtw)
        gl.addWidget(QLabel("Threshold:"))
        self.spin_gt=QSpinBox()
        self.spin_gt.setRange(0, 255)
        gl.addWidget(self.spin_gt)
        tl.addWidget(self.gtw)
        
        self.atw=QWidget()
        al=QVBoxLayout(self.atw)
        l1=QHBoxLayout()
        l1.addWidget(QLabel("Block Size:"))
        self.spin_bs=QSpinBox()
        self.spin_bs.setRange(3, 255)
        self.spin_bs.setSingleStep(2)
        l1.addWidget(self.spin_bs)
        al.addLayout(l1)
        l2=QHBoxLayout()
        l2.addWidget(QLabel("Offset (C):"))
        self.spin_oc=QSpinBox()
        self.spin_oc.setRange(-50, 50)
        l2.addWidget(self.spin_oc)
        al.addLayout(l2)
        tl.addWidget(self.atw)
        group_layout.addWidget(tg)
        layout.addWidget(self.enhancement_group)
        
        for w in [self.cb_grey, self.s_con, self.s_br, self.cb_med, self.cb_sh, self.spin_gt, self.spin_bs, self.spin_oc]:
            w.installEventFilter(self)
        self.combo_tm.currentTextChanged.connect(self._on_threshold_method_changed)

    def eventFilter(self, obj, event):
        if event.type() in [event.MouseButtonRelease, event.KeyRelease]:
            QApplication.instance().callLater(self.update_enhancement_and_view)
        return super().eventFilter(obj, event)

    def _setup_psm_and_regions_panel(self, layout):
        splitter = QSplitter(Qt.Vertical)
        top_widget = QWidget()
        top_layout = QVBoxLayout(top_widget)
        top_layout.setContentsMargins(0,0,0,0)
        method_group = QGroupBox("Structure & Method")
        method_layout = QVBoxLayout(method_group)
        self.cb_text_pdf = QCheckBox("PDF has a text layer")
        self.cb_use_subrows = QCheckBox("Use Sub-rows")
        self.cb_pivot_subrows = QCheckBox("Pivot Sub-rows into Columns")
        self.cb_fix_headers = QCheckBox("Fix Column Headers")
        
        if not PDFPLUMBER_AVAILABLE:
            self.cb_text_pdf.setChecked(False)
            self.cb_text_pdf.setEnabled(False)
            self.cb_text_pdf.setToolTip("Optional library 'pdfplumber' not found.")
        else:
            self.cb_text_pdf.toggled.connect(self._on_extraction_method_changed)
        
        self.cb_use_subrows.setChecked(True)
        self.cb_use_subrows.toggled.connect(self._on_subrow_usage_changed)
        self.cb_pivot_subrows.setToolTip("If checked, data from sub-rows will be placed in one output row with numbered columns (e.g., UTS_1, UTS_2).")
        self.cb_fix_headers.setToolTip("If checked, column header text is stored in the profile. Subsequent processing uses the stored text, not OCR.")
        for w in [self.cb_text_pdf, self.cb_use_subrows, self.cb_pivot_subrows, self.cb_fix_headers]:
            method_layout.addWidget(w)
        top_layout.addWidget(method_group)

        self.psm_group = QGroupBox("Tesseract PSM (for OCR)")
        psm_l = QVBoxLayout(self.psm_group)
        opts = [str(i) for i in range(14)]
        psm_l.addWidget(QLabel("Headers:"))
        self.h_psm = QComboBox()
        self.h_psm.addItems(opts)
        psm_l.addWidget(self.h_psm)
        psm_l.addWidget(QLabel("Data Blocks:"))
        self.d_psm = QComboBox()
        self.d_psm.addItems(opts)
        psm_l.addWidget(self.d_psm)
        top_layout.addWidget(self.psm_group)
        top_layout.addStretch()

        bottom_widget = QWidget()
        bottom_layout = QVBoxLayout(bottom_widget)
        bottom_layout.setContentsMargins(0,0,0,0)
        bottom_layout.addWidget(QLabel("Regions:"))
        self.r_list = QListWidget()
        self.r_list.setSelectionMode(QListWidget.ExtendedSelection)
        self.r_list.itemSelectionChanged.connect(self.on_region_selection_changed)
        bottom_layout.addWidget(self.r_list)
        
        ll = QGridLayout()
        self.btn_link_sub = QPushButton("Link Sub-row to Main")
        self.btn_link_sub.clicked.connect(self.link_sub_row_to_main)
        ll.addWidget(self.btn_link_sub, 0, 0, 1, 2)
        self.btn_link_to_row = QPushButton("Link Data to Row...")
        self.btn_link_to_row.clicked.connect(self.link_to_row)
        ll.addWidget(self.btn_link_to_row, 1, 0)
        self.btn_link_to_col = QPushButton("Link Data to Column...")
        self.btn_link_to_col.clicked.connect(self.link_to_column)
        ll.addWidget(self.btn_link_to_col, 1, 1)
        bottom_layout.addLayout(ll)
        
        self.btn_test = QPushButton("Test Region Extraction")
        self.btn_test.clicked.connect(self.test_extraction_on_selected)
        self.btn_test.setEnabled(False)
        bottom_layout.addWidget(self.btn_test)
        bottom_layout.addWidget(QLabel("Extraction Result:"))
        self.ocr_disp = QTextEdit()
        self.ocr_disp.setReadOnly(True)
        self.ocr_disp.setMaximumHeight(100)
        bottom_layout.addWidget(self.ocr_disp)
        bl = QHBoxLayout()
        self.btn_del = QPushButton("Delete Selected Region")
        self.btn_del.clicked.connect(self.delete_selected_region)
        bl.addWidget(self.btn_del)
        self.btn_save = QPushButton("Save Profile")
        self.btn_save.clicked.connect(self.save_profile)
        bl.addWidget(self.btn_save)
        bottom_layout.addLayout(bl)
        
        splitter.addWidget(top_widget)
        splitter.addWidget(bottom_widget)
        splitter.setSizes([250, 450])
        layout.addWidget(splitter)
        self._on_extraction_method_changed(self.cb_text_pdf.isChecked())
        self._on_subrow_usage_changed(self.cb_use_subrows.isChecked())

    def _on_subrow_usage_changed(self, use_subrows):
        self.btn_link_sub.setVisible(use_subrows)
        self.cb_pivot_subrows.setEnabled(use_subrows)
        if not use_subrows: self.cb_pivot_subrows.setChecked(False)
        self.on_region_selection_changed()

    def _on_extraction_method_changed(self, is_text_based):
        self.psm_group.setEnabled(not is_text_based)
        self.enhancement_group.setEnabled(not is_text_based)
        self.btn_test.setToolTip("Extracts text from PDF text layer." if is_text_based else "Performs OCR on the selected region.")

    def load_pdf_as_image(self, pdf_path, page_num=0, dpi=200):
        # Attempt 1: pdf2image (Poppler)
        try:
            img = convert_from_path(
                pdf_path, 
                dpi=dpi, 
                first_page=page_num + 1, 
                last_page=page_num + 1, 
                poppler_path=current_poppler_path_for_pdf2image
            )[0]
            return img
        except Exception as e1:
            print(f"pdf2image failed: {e1}. Trying pdfplumber fallback.")
            
            # Attempt 2: pdfplumber fallback
            if PDFPLUMBER_AVAILABLE:
                try:
                    with pdfplumber.open(pdf_path) as pdf:
                        if page_num < len(pdf.pages):
                            page_image_obj = pdf.pages[page_num].to_image(resolution=dpi)
                            pil_image = page_image_obj.original

                            if pil_image.mode == 'RGBA':
                                img = Image.new("RGB", pil_image.size, (255, 255, 255))
                                img.paste(pil_image, mask=pil_image.split()[3])
                            else:
                                img = pil_image.convert("RGB")
                            
                            QMessageBox.information(self, "Fallback Used", 
                                                    "Could not load PDF with the primary renderer (Poppler). "
                                                    "A fallback renderer was used successfully. "
                                                    "Image quality may differ.")
                            return img
                        else:
                            raise ValueError(f"Page number {page_num} is out of bounds for the PDF.")
                except Exception as e2:
                    print(f"pdfplumber fallback also failed: {e2}")
                    raise Exception(f"Failed to load PDF image.\n\nPrimary Error (Poppler):\n{e1}\n\nFallback Error (pdfplumber):\n{e2}")
            else:
                raise Exception(f"Failed to load PDF image with primary renderer (Poppler), and the 'pdfplumber' fallback is not available.\n\nError:\n{e1}")

    def load_profile(self):
        try:
            with open(self.loaded_profile_path, 'r') as f: data = json.load(f)
            QMessageBox.information(self, "Profile Loaded", "Profile data loaded. Please select a sample PDF to map regions for editing.")
            self.regions = data.get("regions", {})
            self.psm_config.update(data.get("psm_config", {}))
            self.enhancement_config.update(data.get("enhancement_config", {}))
            self.cb_text_pdf.setChecked(data.get("is_text_based", False))
            self.cb_use_subrows.setChecked(data.get("use_subrows", True))
            self.cb_pivot_subrows.setChecked(data.get("pivot_subrows", False))
            self.cb_fix_headers.setChecked(data.get("fix_column_headers", False))
            self._update_ui_from_config()
            self.refresh_regions_list()
            self._apply_profile_to_sample_pdf(data)
        except Exception as e:
            QMessageBox.critical(self, "Load Error", f"Failed to load profile: {e}\n{traceback.format_exc()}")

    def _apply_profile_to_sample_pdf(self, profile_data):
        path, _ = QFileDialog.getOpenFileName(self, "Load Sample PDF to Edit Profile", "", "PDFs (*.pdf)")
        if not path:
            self.regions = {}
            self.refresh_regions_list()
            return
        self.current_pdf_path = path
        try:
            img = self.load_pdf_as_image(path, page_num=0, dpi=200)
            self.original_pil_image = img
            self.current_image_dims = img.size
            self.scene.clear()
            self.current_pixmap_item = None
            self.scene.setSceneRect(QRectF(0, 0, *self.current_image_dims))
            self.is_calibration_mode = True
            processed_img = MainWindow.apply_image_enhancements(img, self.enhancement_config)
            self.update_canvas_with_image(processed_img)
            self.canvas.fitInView(self.scene.sceneRect(), Qt.KeepAspectRatio)
            
            origin_text = profile_data.get("regions", {}).get("origin", {}).get("text")
            if not origin_text:
                QMessageBox.warning(self, "Mapping Failed", "Profile is missing origin text.")
                return
            origin_coords = MainWindow.find_origin_with_pdfplumber(path, 0, origin_text) if profile_data.get("is_text_based") and PDFPLUMBER_AVAILABLE else self.find_origin_with_ocr(processed_img, origin_text)
            
            if origin_coords:
                ox, oy = origin_coords
                origin_region = self.regions.get('origin', {})
                offset_rect = origin_region.get('offset_rect', [0,0,0.01,0.01])
                origin_region['norm_rect'] = [ox, oy, ox + (offset_rect[2] - offset_rect[0]), oy + (offset_rect[3] - offset_rect[1])]
                for name, data in self.regions.items():
                    if name != 'origin' and 'offset_rect' in data:
                        offset = data['offset_rect']
                        data['norm_rect'] = [offset[0] + ox, offset[1] + oy, offset[2] + ox, offset[3] + oy]
                self.draw_all_defined_regions()
                self.refresh_regions_list()
                QMessageBox.information(self, "Success", "Origin found and profile regions mapped.")
            else:
                QMessageBox.warning(self, "Mapping Failed", f"Could not find origin text '{origin_text}' on the sample PDF.")
                self.regions = {}
                self.refresh_regions_list()
        except Exception as e:
            QMessageBox.critical(self, "Error", f"PDF Load Error for editing: {e}\n{traceback.format_exc()}")

    def find_origin_with_ocr(self, img, text):
        p_data = pytesseract.image_to_data(img, output_type=pytesseract.Output.DICT)
        o_words = text.split()
        for j in range(len(p_data['text']) - len(o_words) + 1):
            sequence = [word for word in p_data['text'][j : j + len(o_words)] if word.strip()]
            if sequence == o_words:
                w, h = img.size
                return (p_data['left'][j] / w, p_data['top'][j] / h)
        return None

    def _update_ui_from_config(self):
        self.h_psm.setCurrentText(self.psm_config.get("row_col_headers_psm", "7"))
        self.d_psm.setCurrentText(self.psm_config.get("data_blocks_psm", "6"))
        self.cb_grey.setChecked(self.enhancement_config.get("to_greyscale", True))
        self.s_con.setValue(int(self.enhancement_config.get("contrast", 1.0) * 100))
        self.s_br.setValue(int(self.enhancement_config.get("brightness", 1.0) * 100))
        self.cb_med.setChecked(self.enhancement_config.get("apply_median", False))
        self.cb_sh.setChecked(self.enhancement_config.get("apply_sharpen", False))
        self.combo_tm.setCurrentText(self.enhancement_config.get("threshold_method", "adaptive"))
        self.spin_gt.setValue(self.enhancement_config.get("global_threshold", 180))
        self.spin_bs.setValue(self.enhancement_config.get("adaptive_block_size", 55))
        self.spin_oc.setValue(self.enhancement_config.get("adaptive_offset", 10))
        self._on_threshold_method_changed(self.combo_tm.currentText())

    def save_profile(self):
        s_name, ok = "", True
        if self.loaded_profile_path:
            s_name = os.path.splitext(os.path.basename(self.loaded_profile_path))[0]
            if QMessageBox.question(self, 'Confirm Save', f"Overwrite profile '{s_name}'?", QMessageBox.Yes | QMessageBox.No, QMessageBox.No) == QMessageBox.No: return
        else:
            s_name, ok = QInputDialog.getText(self, "Save New Profile", "Enter profile name:")
            if not (ok and s_name.strip()): return
            s_name = s_name.strip()

        if 'origin' not in self.regions or 'norm_rect' not in self.regions['origin']:
            QMessageBox.critical(self, "Origin Missing", "Define and map an 'origin' region.")
            return
        
        origin_rect = self.regions['origin']['norm_rect']
        ox, oy = origin_rect[0], origin_rect[1]
        rel_regions = {}
        for n, d in self.regions.items():
            new_d = d.copy()
            rect = new_d.pop('norm_rect', None)
            if rect is None:
                QMessageBox.critical(self, "Save Error", f"Region '{n}' is not mapped.")
                return
            new_d['offset_rect'] = [rect[0] - ox, rect[1] - oy, rect[2] - ox, rect[3] - oy]
            if n == 'origin': new_d['text'] = self.get_region_text('origin', rect)
            if self.cb_fix_headers.isChecked() and n.startswith('col_h'):
                new_d['text'] = self.get_region_text(n, rect)
            rel_regions[n] = new_d
        
        self.update_enhancement_config_from_ui()
        self.psm_config["row_col_headers_psm"] = self.h_psm.currentText()
        self.psm_config["data_blocks_psm"] = self.d_psm.currentText()
        data = {
            "use_subrows": self.cb_use_subrows.isChecked(),
            "pivot_subrows": self.cb_pivot_subrows.isChecked(),
            "is_text_based": self.cb_text_pdf.isChecked(),
            "fix_column_headers": self.cb_fix_headers.isChecked(),
            "regions": rel_regions,
            "psm_config": self.psm_config,
            "enhancement_config": self.enhancement_config
        }
        
        try:
            path = os.path.join(PROFILES_DIR, f"{s_name}.json")
            with open(path, 'w') as f:
                json.dump(data, f, indent=4)
            QMessageBox.information(self, "Saved", f"Profile '{s_name}' saved.")
            self.accept()
        except Exception as e:
            QMessageBox.critical(self, "Save Error", f"Error: {e}")

    def _on_threshold_method_changed(self, method):
        self.gtw.setVisible(method == "global")
        self.atw.setVisible(method != "global")
        self.update_enhancement_and_view()

    def _reset_adjustments(self):
        self.s_con.setValue(100)
        self.s_br.setValue(100)

    def update_enhancement_config_from_ui(self):
        self.enhancement_config.update({
            "to_greyscale": self.cb_grey.isChecked(),
            "contrast": self.s_con.value()/100.0,
            "brightness": self.s_br.value()/100.0,
            "apply_median": self.cb_med.isChecked(),
            "apply_sharpen": self.cb_sh.isChecked(),
            "threshold_method": self.combo_tm.currentText(),
            "global_threshold": self.spin_gt.value(),
            "adaptive_block_size": self.spin_bs.value(),
            "adaptive_offset": self.spin_oc.value()
        })

    def update_enhancement_and_view(self, *args):
        if self.original_pil_image:
            self.update_enhancement_config_from_ui()
            self.processed_pil_image = MainWindow.apply_image_enhancements(self.original_pil_image, self.enhancement_config)
            self.update_canvas_with_image(self.processed_pil_image)
            self.draw_all_defined_regions()
    
    def load_pdf(self):
        path, _ = QFileDialog.getOpenFileName(self, "Open Sample PDF", "", "PDFs (*.pdf)")
        if path:
            self.loaded_profile_path = None
            self.regions = {}
            self.refresh_regions_list()
            self.current_pdf_path = path
            try:
                img = self.load_pdf_as_image(path, page_num=0, dpi=200)
                self.original_pil_image = img
                self.current_image_dims = img.size
                self.scene.clear()
                self.current_pixmap_item = None
                self.is_calibration_mode = True
                self.scene.setSceneRect(QRectF(0, 0, *self.current_image_dims))
                self.update_enhancement_and_view()
                self.canvas.fitInView(self.scene.sceneRect(), Qt.KeepAspectRatio)
            except Exception as e:
                QMessageBox.critical(self, "Error", f"PDF Load Error: {e}\n{traceback.format_exc()}")
            
    def update_canvas_with_image(self, img):
        if self.current_pixmap_item:
            self.scene.removeItem(self.current_pixmap_item)
        qimg_format = QImage.Format_Mono if img.mode == '1' else (QImage.Format_Grayscale8 if img.mode == 'L' else QImage.Format_RGB888)
        qimg = QImage(img.tobytes("raw", img.mode), img.width, img.height, qimg_format)
        self.current_pixmap_item = QGraphicsPixmapItem(QPixmap.fromImage(qimg))
        self.current_pixmap_item.setZValue(-1)
        self.scene.addItem(self.current_pixmap_item)

    def on_region_selection_changed(self):
        s = [i.text().split(' ')[0] for i in self.r_list.selectedItems()]
        has_drawable_selection = len(s) > 0 and 'norm_rect' in self.regions.get(s[0], {})
        main_rows = [i for i in s if i.startswith("row_h")]
        sub_rows = [i for i in s if i.startswith("sub_r")]
        col_headers = [i for i in s if i.startswith("col_h")]
        data_blocks = [i for i in s if i.startswith("data_b")]
        use_subrows = self.cb_use_subrows.isChecked()
        self.btn_link_sub.setEnabled(len(main_rows) == 1 and len(sub_rows) == 1 and use_subrows)
        self.btn_link_to_row.setEnabled(len(data_blocks) == 1 and ((use_subrows and len(sub_rows) == 1) or (not use_subrows and len(main_rows) == 1)))
        self.btn_link_to_col.setEnabled(len(data_blocks) == 1 and len(col_headers) == 1)
        self.btn_test.setEnabled(has_drawable_selection)
    
    def link_to_row(self):
        s = [i.text().split(' ')[0] for i in self.r_list.selectedItems()]
        use_subrows = self.cb_use_subrows.isChecked()
        row_key = next((i for i in s if i.startswith("sub_r" if use_subrows else "row_h")), None)
        block_key = next((i for i in s if i.startswith("data_b")), None)
        if not row_key or not block_key: return
        cols = {k: v for k, v in self.regions.items() if k.startswith("col_h")}
        if not cols:
            QMessageBox.warning(self, "No Columns", "Define 'col_header_X' regions first.")
            return
        block_region_data = self.regions.get(block_key)
        if not block_region_data or 'norm_rect' not in block_region_data: return
        text_to_chunk = self.get_region_text(block_key, block_region_data['norm_rect'])
        block_data = self.regions[block_key]
        for key in ['link_type', 'links_to_col']:
            block_data.pop(key, None)
        cfg = block_data.get('chunking_config', {'method': 'Smart Split', 'param': ''})
        dlg = DataLinkerDialog(cols, text_to_chunk, cfg, is_row_linking=True, parent=self)
        if dlg.exec_():
            block_data['link_type'] = 'row'
            block_data['chunking_config'] = dlg.get_config()
            block_data['links_to_sub_row' if use_subrows else 'links_to_row'] = row_key
            self.refresh_regions_list()
            QMessageBox.information(self, "Row Link Saved", f"Mapping for '{block_key}' saved.")

    def link_to_column(self):
        s = [i.text().split(' ')[0] for i in self.r_list.selectedItems()]
        col_key = next((i for i in s if i.startswith("col_h")), None)
        block_key = next((i for i in s if i.startswith("data_b")), None)
        if not col_key or not block_key: return
        rows = {k: v for k, v in self.regions.items() if k.startswith("sub_r" if self.cb_use_subrows.isChecked() else "row_h")}
        if not rows:
            QMessageBox.warning(self, "No Rows", "Define row regions first.")
            return
        block_region_data = self.regions.get(block_key)
        if not block_region_data or 'norm_rect' not in block_region_data: return
        text_to_chunk = self.get_region_text(block_key, block_region_data['norm_rect'])
        block_data = self.regions[block_key]
        for k in ['link_type', 'chunking_config', 'links_to_row', 'links_to_sub_row']:
            block_data.pop(k, None)
        cfg = block_data.get('chunking_config', {'method': 'Newline Delimited', 'param': ''})
        dlg = DataLinkerDialog(rows, text_to_chunk, cfg, is_row_linking=False, parent=self)
        if dlg.exec_():
            block_data['link_type'] = 'column'
            block_data['chunking_config'] = dlg.get_config()
            block_data['links_to_col'] = col_key
            self.refresh_regions_list()
            QMessageBox.information(self, "Column Link Saved", f"'{block_key}' is now linked to column '{col_key}'.")

    def link_sub_row_to_main(self):
        s = [i.text().split(' ')[0] for i in self.r_list.selectedItems()]
        main = next((i for i in s if i.startswith("row_h")), None)
        sub = next((i for i in s if i.startswith("sub_r")), None)
        if main and sub and sub in self.regions:
            self.regions[sub]['links_to_main_row'] = main
            self.refresh_regions_list()

    def get_region_text(self, name, norm_rect):
        if self.cb_text_pdf.isChecked():
            if self.current_pdf_path:
                return MainWindow.text_layer_region(self.current_pdf_path, 0, norm_rect, name)
        elif self.processed_pil_image:
            psm = self.d_psm.currentText() if name.startswith("data_b") else self.h_psm.currentText()
            return MainWindow.ocr_region(self.processed_pil_image, norm_rect, psm, name)
        return ""

    def test_extraction_on_selected(self):
        item = self.r_list.currentItem()
        if not item: return
        name = item.text().split(' ')[0]
        region_data = self.regions.get(name)
        if not region_data or 'norm_rect' not in region_data: return
        text = self.get_region_text(name, region_data['norm_rect'])
        mode = "Text Layer" if self.cb_text_pdf.isChecked() else "OCR"
        self.ocr_disp.setText(f"--'{name}' ({mode})--\n{text}")

    def add_region_from_canvas(self, rect):
        name_text = self.name_input.text().strip()
        if not name_text:
            name_text, ok = QInputDialog.getText(self, "Name", "Name (e.g. origin, col_header_1):")
            if not (ok and name_text):
                if self.canvas.current_rect_item: self.scene.removeItem(self.canvas.current_rect_item)
                return
        name = name_text
        self.name_input.clear()
        w, h = self.current_image_dims
        self.regions[name] = {'norm_rect': [rect.left() / w, rect.top() / h, rect.right() / w, rect.bottom() / h]}
        self.refresh_regions_list()
        pr = QGraphicsRectItem(rect)
        pr.setPen(QPen(QColor("blue"), 2))
        self.scene.addItem(pr)
        t = self.scene.addText(name)
        t.setPos(rect.topLeft() + QPointF(2, -18))
        t.setDefaultTextColor(QColor("blue"))
        if self.canvas.current_rect_item:
            self.scene.removeItem(self.canvas.current_rect_item)
            self.canvas.current_rect_item = None
            
    def refresh_regions_list(self):
        self.r_list.clear()
        for name in sorted(self.regions.keys()):
            text = name
            data = self.regions[name]
            if data.get('link_type') == 'row':
                link_target = data.get('links_to_sub_row') or data.get('links_to_row')
                text += f" (to row: {link_target})"
            elif data.get('link_type') == 'column':
                text += f" (to col: {data.get('links_to_col')})"
            if data.get('links_to_main_row'):
                text += f" (main: {data.get('links_to_main_row')})"
            if 'norm_rect' not in data:
                text += " (Relative)"
            self.r_list.addItem(QListWidgetItem(text))

    def draw_all_defined_regions(self):
        for item in [i for i in self.scene.items() if (isinstance(i, QGraphicsRectItem) and i != self.canvas.current_rect_item) or isinstance(i, QGraphicsTextItem)]:
            self.scene.removeItem(item)
        if self.current_image_dims:
            w, h = self.current_image_dims
            for name, data in self.regions.items():
                if 'norm_rect' in data:
                    r = data['norm_rect']
                    rect = QRectF(QPointF(r[0] * w, r[1] * h), QPointF(r[2] * w, r[3] * h))
                    item = QGraphicsRectItem(rect.normalized())
                    item.setPen(QPen(QColor("blue"), 2))
                    self.scene.addItem(item)
                    text = self.scene.addText(name)
                    text.setPos(rect.topLeft() + QPointF(2, -18))
                    text.setDefaultTextColor(QColor("blue"))
                    
    def delete_selected_region(self):
        for item in self.r_list.selectedItems():
            name = item.text().split(' ')[0]
            if name in self.regions: del self.regions[name]
            for region_data in self.regions.values():
                for link_key in ['links_to_main_row', 'links_to_sub_row', 'links_to_row', 'links_to_col']:
                    if region_data.get(link_key) == name:
                        region_data.pop(link_key, None)
        self.refresh_regions_list()
        self.draw_all_defined_regions()


class ManageProfilesDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Manage Profiles & Cache")
        self.setMinimumWidth(400)
        self.layout = QVBoxLayout(self)
        profile_group = QGroupBox("Delete Profiles")
        profile_layout = QVBoxLayout(profile_group)
        self.profile_list = QListWidget()
        self.profile_list.setSelectionMode(QListWidget.ExtendedSelection)
        self.populate_profiles()
        profile_layout.addWidget(self.profile_list)
        self.btn_delete_profile = QPushButton("Delete Selected Profile(s)")
        self.btn_delete_profile.clicked.connect(self.delete_profiles)
        profile_layout.addWidget(self.btn_delete_profile)
        self.layout.addWidget(profile_group)
        cache_group = QGroupBox("Manage Cache")
        cache_layout = QVBoxLayout(cache_group)
        self.btn_clear_cache = QPushButton("Clear Processing Cache")
        self.btn_clear_cache.setToolTip("Deletes all cached results, forcing re-processing of all files.")
        self.btn_clear_cache.clicked.connect(self.clear_cache)
        cache_layout.addWidget(self.btn_clear_cache)
        self.layout.addWidget(cache_group)
        self.buttons = QDialogButtonBox(QDialogButtonBox.Close)
        self.buttons.rejected.connect(self.reject)
        self.layout.addWidget(self.buttons)

    def populate_profiles(self):
        self.profile_list.clear()
        self.profile_list.addItems(sorted([os.path.splitext(p)[0] for p in os.listdir(PROFILES_DIR) if p.endswith('.json')]))
        
    def delete_profiles(self):
        if not self.profile_list.selectedItems(): return
        if QMessageBox.question(self, "Confirm Deletion", f"Permanently delete {len(self.profile_list.selectedItems())} profile(s)?", QMessageBox.Yes | QMessageBox.No, QMessageBox.No) == QMessageBox.Yes:
            for item in self.profile_list.selectedItems():
                try:
                    os.remove(os.path.join(PROFILES_DIR, f"{item.text()}.json"))
                except Exception as e:
                    QMessageBox.critical(self, "Error", f"Could not delete profile '{item.text()}': {e}")
            self.populate_profiles()
            if self.parent(): self.parent().update_all_profile_combos()

    def clear_cache(self):
        if QMessageBox.question(self, "Confirm Clear Cache", "Delete all cached processing results?", QMessageBox.Yes | QMessageBox.No, QMessageBox.No) == QMessageBox.Yes:
            try:
                if os.path.exists(cache_full_path): shutil.rmtree(cache_full_path)
                os.makedirs(cache_full_path)
                QMessageBox.information(self, "Success", "Cache has been cleared.")
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Could not clear cache: {e}")


class SelectProfileDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Select Profile")
        self.layout=QVBoxLayout(self)
        self.layout.addWidget(QLabel("Select an existing profile:"))
        self.selected_profile_path = None
        self.combo=QComboBox()
        self.layout.addWidget(self.combo)
        btns=QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        self.btn_ok = btns.button(QDialogButtonBox.Ok)
        btns.accepted.connect(self.accept_selection)
        btns.rejected.connect(self.reject)
        self.layout.addWidget(btns)
        self.populate()

    def populate(self):
        profiles = sorted([os.path.splitext(p)[0] for p in os.listdir(PROFILES_DIR) if p.endswith('.json')])
        self.combo.clear()
        if profiles:
            self.combo.addItems(profiles)
            self.combo.setEnabled(True)
            self.btn_ok.setEnabled(True)
        else:
            self.combo.addItem("No profiles found")
            self.combo.setEnabled(False)
            self.btn_ok.setEnabled(False)

    def accept_selection(self):
        name = self.combo.currentText()
        if name and name != "No profiles found":
            self.selected_profile_path = os.path.join(PROFILES_DIR, f"{name}.json")
            self.accept()
        else:
            QMessageBox.warning(self, "No Profile", "Please select a valid profile.")


class MergeConfigDialog(QDialog):
    def __init__(self, dataframe, columns, parent=None, existing_config=None):
        super().__init__(parent)
        self.setWindowTitle("Advanced Merge Configuration")
        self.setMinimumSize(900, 700)
        self.layout = QVBoxLayout(self)
        self.config = existing_config or {}
        self.columns = columns
        self.dataframe = dataframe
        instructions = QLabel("Configure how to merge rows with the same key.<br>1. Mark one or more columns as a <b>Group Key</b>.<br>2. For all other columns, choose an <b>Aggregation</b> method.<br>3. Uncheck columns to exclude them.<br>4. Click <b>Refresh Preview</b> to see the result.")
        instructions.setWordWrap(True)
        self.layout.addWidget(instructions)
        self.splitter = QSplitter(Qt.Vertical)
        self.layout.addWidget(self.splitter)

        config_widget = QWidget()
        config_layout = QVBoxLayout(config_widget)
        config_layout.setContentsMargins(0,0,0,0)
        self.table = QTableWidget()
        self.table.setColumnCount(3)
        self.table.setHorizontalHeaderLabels(["Column Name", "Include", "Aggregation / Action"])
        self.table.setRowCount(len(columns))
        agg_options = ["Group Key (Index)", "Join Text", "Sum", "Average", "First Value", "Last Value", "Min Value", "Max Value"]

        for i, col_name in enumerate(columns):
            self.table.setItem(i, 0, QTableWidgetItem(col_name))
            chk_box_item = QTableWidgetItem()
            chk_box_item.setFlags(Qt.ItemIsUserCheckable | Qt.ItemIsEnabled)
            chk_box_item.setCheckState(Qt.Checked)
            self.table.setItem(i, 1, chk_box_item)
            combo = QComboBox()
            combo.addItems(agg_options)
            
            # Pre-populate based on existing config
            is_group_key = col_name in self.config.get('group_by_columns', [])
            agg_method = self.config.get('aggregations', {}).get(col_name)

            if is_group_key:
                combo.setCurrentText("Group Key (Index)")
            elif agg_method:
                method_map = {"sum": "Sum", "mean": "Average", "first": "First Value", "last": "Last Value", "min": "Min Value", "max": "Max Value"}
                combo.setCurrentText(method_map.get(agg_method, "Join Text"))
            elif "header" in col_name.lower():
                combo.setCurrentText("Group Key (Index)")
            elif any(kw in col_name.lower() for kw in ["qty", "quantity", "amount", "total", "price"]):
                combo.setCurrentText("Sum")
            else:
                combo.setCurrentText("Join Text")
            self.table.setCellWidget(i, 2, combo)

        self.table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)
        self.table.item(0,0).setFlags(self.table.item(0,0).flags() & ~Qt.ItemIsEditable)
        config_layout.addWidget(self.table)
        self.splitter.addWidget(config_widget)
        
        preview_widget = QWidget()
        preview_layout = QVBoxLayout(preview_widget)
        preview_layout.setContentsMargins(0,0,0,0)
        preview_group = QGroupBox("Preview of Merged Data")
        preview_group_layout = QVBoxLayout(preview_group)
        self.btn_refresh_preview = QPushButton("Refresh Preview")
        self.btn_refresh_preview.clicked.connect(self.refresh_preview)
        preview_group_layout.addWidget(self.btn_refresh_preview)
        self.preview_table = QTableWidget()
        self.preview_table.setEditTriggers(QTableWidget.NoEditTriggers)
        preview_group_layout.addWidget(self.preview_table)
        preview_layout.addWidget(preview_group)
        self.splitter.addWidget(preview_widget)
        self.splitter.setSizes([300, 400])

        save_layout = QHBoxLayout()
        self.save_check = QCheckBox("Save this configuration")
        self.config_name_input = QLineEdit()
        self.config_name_input.setPlaceholderText("Enter configuration name...")
        self.config_name_input.setEnabled(False)
        self.save_check.toggled.connect(self.config_name_input.setEnabled)
        save_layout.addWidget(self.save_check)
        save_layout.addWidget(self.config_name_input)
        self.layout.addLayout(save_layout)
        
        self.button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        self.button_box.accepted.connect(self.accept)
        self.button_box.rejected.connect(self.reject)
        self.layout.addWidget(self.button_box)
        self.refresh_preview()

    def _build_config_from_ui(self):
        group_by_cols, aggregations, included_cols, ordered_cols = [], {}, [], []
        for i in range(self.table.rowCount()):
            col_name = self.table.item(i, 0).text()
            ordered_cols.append(col_name)
            if self.table.item(i, 1).checkState() == Qt.Checked:
                included_cols.append(col_name)
                agg_method = self.table.cellWidget(i, 2).currentText()
                if agg_method == "Group Key (Index)":
                    group_by_cols.append(col_name)
                else:
                    aggregations[col_name] = agg_method
        if not group_by_cols:
            return None
        return {"group_by_columns": group_by_cols, "aggregations": aggregations, "columns_to_include": [c for c in ordered_cols if c in included_cols]}

    def refresh_preview(self):
        config = self._build_config_from_ui()
        if not config:
            self.preview_table.setRowCount(0)
            self.preview_table.setColumnCount(1)
            self.preview_table.setHorizontalHeaderLabels(["Invalid: Select at least one 'Group Key'"])
            return
        self.display_df_in_table(MainWindow.perform_merge(self.dataframe, config), self.preview_table)

    def display_df_in_table(self, df, table_widget):
        table_widget.clear()
        if df is None or df.empty:
            table_widget.setRowCount(0)
            table_widget.setColumnCount(0)
            return
        table_widget.setRowCount(df.shape[0])
        table_widget.setColumnCount(df.shape[1])
        table_widget.setHorizontalHeaderLabels(df.columns)
        for row in range(df.shape[0]):
            for col in range(df.shape[1]):
                table_widget.setItem(row, col, QTableWidgetItem(str(df.iat[row, col]) if pd.notna(df.iat[row, col]) else ""))
        table_widget.resizeColumnsToContents()

    def accept(self):
        config = self._build_config_from_ui()
        if not config:
            QMessageBox.warning(self, "Config Error", "You must select at least one column as a 'Group Key (Index)'.")
            return
        self.config = config
        if self.save_check.isChecked():
            config_name = self.config_name_input.text().strip()
            if not config_name:
                QMessageBox.warning(self, "Invalid Name", "Please enter a valid name for the merge configuration.")
                return
            try:
                with open(os.path.join(MERGE_CONFIGS_DIR, f"{config_name}.json"), 'w') as f:
                    json.dump(self.config, f, indent=4)
            except Exception as e:
                QMessageBox.critical(self, "Save Error", f"Could not save merge configuration: {e}")
                return
        super().accept()

    def get_config(self):
        return self.config

# --- START: Bulk Report Generation & Database Classes ---

class DroppableTableWidget(QTableWidget):
    item_mapped = pyqtSignal(str, str)
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setAcceptDrops(True)
        self.setEditTriggers(QAbstractItemView.NoEditTriggers)
    def dragEnterEvent(self, event):
        if event.mimeData().hasText(): event.acceptProposedAction()
    def dragMoveEvent(self, event):
        if event.mimeData().hasText(): event.acceptProposedAction()
    def dropEvent(self, event):
        if event.mimeData().hasText():
            pos = event.pos()
            row = self.rowAt(pos.y())
            col = self.columnAt(pos.x())
            if row >= 0 and col >= 0:
                self.item_mapped.emit(event.mimeData().text(), self.get_excel_cell_address(row, col))
    @staticmethod
    def get_excel_cell_address(row, col):
        col_name = ''
        n = col + 1
        while n > 0:
            n, remainder = divmod(n - 1, 26)
            col_name = chr(65 + remainder) + col_name
        return f"{col_name}{row + 1}"

class ManageBulkProfilesDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Manage Bulk Report Profiles")
        self.setMinimumWidth(400)
        self.layout = QVBoxLayout(self)
        self.profile_list = QListWidget()
        self.profile_list.setSelectionMode(QListWidget.ExtendedSelection)
        self.populate_profiles()
        self.layout.addWidget(self.profile_list)
        self.btn_delete = QPushButton("Delete Selected Profile(s)")
        self.btn_delete.clicked.connect(self.delete_profiles)
        self.layout.addWidget(self.btn_delete)
        self.buttons = QDialogButtonBox(QDialogButtonBox.Close)
        self.buttons.rejected.connect(self.reject)
        self.layout.addWidget(self.buttons)

    def populate_profiles(self):
        self.profile_list.clear()
        self.profile_list.addItems(sorted([os.path.splitext(p)[0] for p in os.listdir(BULK_PROFILES_DIR) if p.endswith('.json')]))
    def delete_profiles(self):
        if not self.profile_list.selectedItems(): return
        if QMessageBox.question(self, "Confirm Deletion", f"Permanently delete {len(self.profile_list.selectedItems())} profile(s)?", QMessageBox.Yes | QMessageBox.No, QMessageBox.No) == QMessageBox.Yes:
            for item in self.profile_list.selectedItems():
                try:
                    os.remove(os.path.join(BULK_PROFILES_DIR, f"{item.text()}.json"))
                except Exception as e:
                    QMessageBox.critical(self, "Error", f"Could not delete '{item.text()}': {e}")
            self.populate_profiles()
            if self.parent() and hasattr(self.parent(), 'populate_profiles_combo'):
                self.parent().populate_profiles_combo()

class BulkReportMappingDialog(QDialog):
    def __init__(self, data_columns, template_path, existing_config=None, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Configure Bulk Report Mapping")
        self.setMinimumSize(1000, 800)
        self.data_columns = data_columns
        self.template_path = template_path
        self.mappings = existing_config.get('mappings', {}) if existing_config else {}
        self.filename_column = existing_config.get('filename_column') if existing_config else None
        self.layout = QVBoxLayout(self)
        self._setup_ui()
        self._populate_template_preview()
        self._update_ui_from_config()

    def _setup_ui(self):
        filename_layout = QHBoxLayout()
        filename_layout.addWidget(QLabel("<b>1. Select Column for Report Filename:</b>"))
        self.combo_filename_col = QComboBox()
        self.combo_filename_col.addItems(["<Select a Column>"] + self.data_columns)
        filename_layout.addWidget(self.combo_filename_col)
        filename_layout.addStretch()
        self.layout.addLayout(filename_layout)
        
        self.layout.addWidget(QLabel("<b>2. Drag a Data Column and Drop it onto a Template Cell to map them.</b>"))
        splitter = QSplitter(Qt.Horizontal)
        
        data_group = QGroupBox("Data Columns (Source)")
        data_layout = QVBoxLayout(data_group)
        self.data_list = DraggableListWidget()
        self.data_list.addItems(self.data_columns)
        data_layout.addWidget(self.data_list)
        splitter.addWidget(data_group)
        
        template_group = QGroupBox("Template Preview (Destination)")
        template_layout = QVBoxLayout(template_group)
        self.template_table = DroppableTableWidget()
        self.template_table.item_mapped.connect(self.on_cell_mapped)
        template_layout.addWidget(self.template_table)
        splitter.addWidget(template_group)
        
        splitter.setSizes([200, 800])
        self.layout.addWidget(splitter)
        
        self.button_box = QDialogButtonBox(QDialogButtonBox.Save | QDialogButtonBox.Cancel)
        self.button_box.accepted.connect(self.accept_config)
        self.button_box.rejected.connect(self.reject)
        self.layout.addWidget(self.button_box)
        
    def _populate_template_preview(self):
        try:
            wb = openpyxl.load_workbook(self.template_path, data_only=True)
            ws = wb.active
            max_rows = min(ws.max_row, 100)
            max_cols = min(ws.max_column, 52)
            self.template_table.setRowCount(max_rows)
            self.template_table.setColumnCount(max_cols)
            self.template_table.setHorizontalHeaderLabels([DroppableTableWidget.get_excel_cell_address(0, i)[:-1] for i in range(max_cols)])
            for r_idx, row in enumerate(ws.iter_rows(min_row=1, max_row=max_rows, min_col=1, max_col=max_cols)):
                for c_idx, cell in enumerate(row):
                    self.template_table.setItem(r_idx, c_idx, QTableWidgetItem(str(cell.value) if cell.value is not None else ""))
            self.template_table.resizeColumnsToContents()
        except Exception as e:
            QMessageBox.critical(self, "Template Error", f"Failed to load and preview template:\n{e}")
            self.close()

    def on_cell_mapped(self, source_column, cell_address):
        self.mappings[source_column] = cell_address
        self._update_template_visuals()
        
    def _update_ui_from_config(self):
        if self.filename_column and self.filename_column in self.data_columns:
            self.combo_filename_col.setCurrentText(self.filename_column)
        self._update_template_visuals()
        
    def _update_template_visuals(self):
        cell_to_column_map = {v: k for k, v in self.mappings.items()}
        for r in range(self.template_table.rowCount()):
            for c in range(self.template_table.columnCount()):
                cell_addr = self.template_table.get_excel_cell_address(r, c)
                item = self.template_table.item(r, c)
                if item and cell_addr in cell_to_column_map:
                    item.setText(f'<< {cell_to_column_map[cell_addr]} >>')
                    item.setBackground(QColor("#d4edda"))
                    item.setToolTip(f"Mapped to '{cell_to_column_map[cell_addr]}'")
                    
    def get_config(self):
        return {
            "template_path": self.template_path,
            "filename_column": self.combo_filename_col.currentText(),
            "mappings": self.mappings
        }
        
    def accept_config(self):
        if self.combo_filename_col.currentIndex() == 0:
            QMessageBox.warning(self, "Incomplete", "Please select a column for report filenames.")
            return
        if not self.mappings:
            QMessageBox.warning(self, "Incomplete", "Please map at least one data column.")
            return
        self.accept()

class BulkReportDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Bulk Report Generator")
        self.setMinimumSize(800, 550)
        self.data_df = None
        self.data_file_path = None
        self.template_file_path = None
        self.layout = QVBoxLayout(self)
        self._setup_ui()
        self.populate_profiles_combo()

    def _setup_ui(self):
        source_group = QGroupBox("1. Select Data Source")
        source_layout = QVBoxLayout(source_group)
        self.radio_from_file = QRadioButton("Load from File")
        self.radio_from_db = QRadioButton("Use Integral Database")
        self.radio_from_file.toggled.connect(self._on_data_source_changed)
        file_layout = QHBoxLayout()
        self.btn_load_data = QPushButton("Load Data Excel...")
        self.btn_load_data.clicked.connect(self.load_data_excel)
        self.lbl_data_path = QLabel("No data file loaded.")
        file_layout.addWidget(self.btn_load_data)
        file_layout.addWidget(self.lbl_data_path, 1)
        source_layout.addWidget(self.radio_from_file)
        source_layout.addLayout(file_layout)
        source_layout.addWidget(self.radio_from_db)
        self.layout.addWidget(source_group)
        self.radio_from_file.setChecked(True)

        profile_group = QGroupBox("2. Select Template & Mapping Profile")
        profile_layout = QGridLayout(profile_group)
        self.btn_load_template = QPushButton("Load Template Excel...")
        self.btn_load_template.clicked.connect(self.load_template_excel)
        self.lbl_template_path = QLabel("No template file loaded.")
        profile_layout.addWidget(self.btn_load_template, 0, 0)
        profile_layout.addWidget(self.lbl_template_path, 0, 1, 1, 3)
        profile_layout.addWidget(QLabel("Profile:"), 1, 0)
        self.combo_profiles = QComboBox()
        self.combo_profiles.currentIndexChanged.connect(self.on_profile_selected)
        profile_layout.addWidget(self.combo_profiles, 1, 1)
        self.btn_create_edit = QPushButton("Create / Edit...")
        self.btn_create_edit.clicked.connect(self.create_edit_profile)
        profile_layout.addWidget(self.btn_create_edit, 1, 2)
        self.btn_manage = QPushButton("Manage...")
        self.btn_manage.clicked.connect(self.manage_profiles)
        profile_layout.addWidget(self.btn_manage, 1, 3)
        self.layout.addWidget(profile_group)
        
        gen_group = QGroupBox("3. Generate Bulk Reports")
        gen_layout = QVBoxLayout(gen_group)
        self.btn_generate = QPushButton("Generate All Reports")
        self.btn_generate.clicked.connect(self.generate_reports)
        self.progress_bar = QProgressBar()
        gen_layout.addWidget(self.btn_generate)
        gen_layout.addWidget(self.progress_bar)
        self.layout.addWidget(gen_group)
        
        single_gen_group = QGroupBox("4. Generate Single Report")
        single_gen_layout = QHBoxLayout(single_gen_group)
        single_gen_layout.addWidget(QLabel("Value to Match in Filename Column:"))
        self.single_report_input = QLineEdit()
        self.single_report_input.setPlaceholderText("Enter exact value...")
        single_gen_layout.addWidget(self.single_report_input)
        self.btn_generate_single = QPushButton("Generate Single Report")
        self.btn_generate_single.clicked.connect(self.generate_single_report)
        single_gen_layout.addWidget(self.btn_generate_single)
        self.layout.addWidget(single_gen_group)
        
        self.button_box = QDialogButtonBox(QDialogButtonBox.Close)
        self.button_box.rejected.connect(self.reject)
        self.layout.addWidget(self.button_box)

    def _on_data_source_changed(self, checked):
        if checked:
            self.btn_load_data.setEnabled(True)
            self.lbl_data_path.setText("No data file loaded." if not self.data_file_path else os.path.basename(self.data_file_path))
        else:
            self.btn_load_data.setEnabled(False)
            self.load_integral_database()

    def load_integral_database(self):
        if not os.path.exists(integral_database_path):
            QMessageBox.warning(self, "DB Not Found", f"'{INTEGRAL_DATABASE_FILE}' not found. Please create it via 'Manage Database'.")
            self.lbl_data_path.setText("Database not found.")
            self.radio_from_file.setChecked(True)
            self.data_df = None
            return
        try:
            self.data_df = pd.read_excel(integral_database_path)
            self.data_file_path = None
            self.lbl_data_path.setText(f"Using '{INTEGRAL_DATABASE_FILE}' ({len(self.data_df)} rows)")
        except Exception as e:
            QMessageBox.critical(self, "DB Error", f"Could not load database:\n{e}")
            self.lbl_data_path.setText("Error loading database.")
            self.radio_from_file.setChecked(True)
            self.data_df = None

    def populate_profiles_combo(self):
        current_selection = self.combo_profiles.currentText()
        self.combo_profiles.blockSignals(True)
        self.combo_profiles.clear()
        profiles = sorted([os.path.splitext(p)[0] for p in os.listdir(BULK_PROFILES_DIR) if p.endswith('.json')])
        if profiles: self.combo_profiles.addItems(["<Select Profile>"] + profiles)
        else: self.combo_profiles.addItem("No profiles found")
        if current_selection in profiles: self.combo_profiles.setCurrentText(current_selection)
        self.combo_profiles.blockSignals(False)
        self.on_profile_selected(self.combo_profiles.currentIndex())

    def on_profile_selected(self, index):
        profile_name = self.combo_profiles.currentText()
        if profile_name in ["<Select Profile>", "No profiles found"]:
            self.template_file_path = None
            self.lbl_template_path.setText("No template file loaded.")
            return
        try:
            with open(os.path.join(BULK_PROFILES_DIR, f"{profile_name}.json"), 'r') as f: config = json.load(f)
            linked_template = config.get("template_path")
            if linked_template and os.path.exists(linked_template):
                self.template_file_path = linked_template
                self.lbl_template_path.setText(os.path.basename(linked_template))
            elif linked_template:
                QMessageBox.warning(self, "Template Not Found", f"Template not found:\n{linked_template}\nLoad it manually.")
                self.template_file_path = None
                self.lbl_template_path.setText("Linked template not found.")
            else:
                self.template_file_path = None
                self.lbl_template_path.setText("No template linked in profile.")
        except Exception as e:
            QMessageBox.critical(self, "Profile Load Error", f"Could not read profile '{profile_name}':\n{e}")
            
    def load_data_excel(self, path=None):
        if not path:
            path, _ = QFileDialog.getOpenFileName(self, "Select Data Excel", "", "Excel Files (*.xlsx *.xls)")
        if not path: return
        try:
            self.data_df = pd.read_excel(path)
            self.data_file_path = path
            self.lbl_data_path.setText(os.path.basename(path))
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Could not load data Excel:\n{e}")
            self.data_df = None
            self.data_file_path = None
            self.lbl_data_path.setText("Load failed.")

    def load_template_excel(self):
        path, _ = QFileDialog.getOpenFileName(self, "Select Template Excel", "", "Excel Files (*.xlsx)")
        if path:
            self.template_file_path = path
            self.lbl_template_path.setText(os.path.basename(path))

    def manage_profiles(self):
        dlg = ManageBulkProfilesDialog(self)
        dlg.exec_()
        
    def create_edit_profile(self):
        if self.data_df is None:
            QMessageBox.warning(self, "Data Missing", "Please load a data source first.")
            return
        if self.template_file_path is None:
            QMessageBox.warning(self, "Template Missing", "Please load a Template Excel file first.")
            return
            
        profile_name = self.combo_profiles.currentText()
        existing_config = None
        if profile_name not in ["<Select Profile>", "No profiles found"]:
            try:
                with open(os.path.join(BULK_PROFILES_DIR, f"{profile_name}.json"), 'r') as f:
                    existing_config = json.load(f)
            except Exception as e:
                QMessageBox.warning(self, "Profile Load Error", f"Could not load '{profile_name}': {e}")
        
        dlg = BulkReportMappingDialog(self.data_df.columns.tolist(), self.template_file_path, existing_config, self)
        if dlg.exec_():
            config = dlg.get_config()
            new_name, ok = QInputDialog.getText(self, "Save Profile", "Enter profile name:", text=profile_name if existing_config else "")
            if ok and new_name.strip():
                try:
                    path = os.path.join(BULK_PROFILES_DIR, f"{new_name.strip()}.json")
                    with open(path, 'w') as f:
                        json.dump(config, f, indent=4)
                    QMessageBox.information(self, "Success", f"Profile '{new_name.strip()}' saved.")
                    self.populate_profiles_combo()
                    self.combo_profiles.setCurrentText(new_name.strip())
                except Exception as e:
                    QMessageBox.critical(self, "Save Error", f"Failed to save profile: {e}")

    def generate_reports(self):
        if self.data_df is None or self.template_file_path is None or self.combo_profiles.currentText() in ["<Select Profile>", "No profiles found"]:
            QMessageBox.warning(self, "Incomplete Setup", "Please ensure Data Source, Template, and a Profile are all selected.")
            return
        profile_name = self.combo_profiles.currentText()
        try:
            with open(os.path.join(BULK_PROFILES_DIR, f"{profile_name}.json"), 'r') as f: config = json.load(f)
        except Exception as e:
            QMessageBox.critical(self, "Profile Error", f"Failed to load profile '{profile_name}':\n{e}")
            return

        mappings = config.get("mappings", {})
        filename_col = config.get("filename_column")
        if not mappings or not filename_col or filename_col not in self.data_df.columns:
            QMessageBox.critical(self, "Invalid Profile", "Profile is invalid (check mappings and filename column).")
            return
            
        output_folder = os.path.join(BULK_REPORTS_DIR, profile_name)
        os.makedirs(output_folder, exist_ok=True)
        self.progress_bar.setRange(0, len(self.data_df))
        
        for index, row in self.data_df.iterrows():
            QApplication.processEvents()
            try:
                workbook = openpyxl.load_workbook(self.template_file_path)
                sheet = workbook.active
                for source_col, cell_address in mappings.items():
                    if source_col in row:
                        sheet[cell_address] = row[source_col]
                fname = str(row[filename_col])
                sanitized_fname = re.sub(r'[\\/*?:"<>|]', "_", fname)
                workbook.save(os.path.join(output_folder, f"{sanitized_fname}.xlsx"))
            except Exception as e:
                err_msg = f"Failed for row {index+1} (Filename: {row.get(filename_col, 'N/A')}).\nError: {e}"
                if QMessageBox.critical(self, "Gen Error", err_msg, QMessageBox.Abort | QMessageBox.Ignore) == QMessageBox.Abort:
                    self.progress_bar.setValue(0)
                    return
            self.progress_bar.setValue(index + 1)
        QMessageBox.information(self, "Complete", f"Generated {len(self.data_df)} reports in:\n{output_folder}")
        self.progress_bar.setValue(0)

    def generate_single_report(self):
        if self.data_df is None or self.template_file_path is None or self.combo_profiles.currentText() in ["<Select Profile>", "No profiles found"]:
            QMessageBox.warning(self, "Incomplete Setup", "Ensure Data Source, Template, and a Profile are selected.")
            return
        match_value = self.single_report_input.text().strip()
        if not match_value:
            QMessageBox.warning(self, "Input Missing", "Please enter a value to match.")
            return
        try:
            with open(os.path.join(BULK_PROFILES_DIR, f"{self.combo_profiles.currentText()}.json"), 'r') as f: config = json.load(f)
        except Exception as e:
            QMessageBox.critical(self, "Profile Error", f"Failed to load profile:\n{e}")
            return
        
        mappings = config.get("mappings", {})
        filename_col = config.get("filename_column")
        if not filename_col or filename_col not in self.data_df.columns:
            QMessageBox.critical(self, "Invalid Profile", "Profile has no valid filename column.")
            return
        matching_rows = self.data_df[self.data_df[filename_col].astype(str) == match_value]
        if len(matching_rows) == 0:
            QMessageBox.information(self, "Not Found", f"No row found where '{filename_col}' is '{match_value}'.")
            return
        if len(matching_rows) > 1:
            QMessageBox.warning(self, "Multiple Matches", f"Found {len(matching_rows)} rows for '{match_value}'.")
            return

        row = matching_rows.iloc[0]
        sanitized_fname = re.sub(r'[\\/*?:"<>|]', "_", match_value)
        save_path, _ = QFileDialog.getSaveFileName(self, "Save Single Report", f"{sanitized_fname}.xlsx", "Excel Files (*.xlsx)")
        if not save_path: return
        try:
            workbook = openpyxl.load_workbook(self.template_file_path)
            sheet = workbook.active
            for source_col, cell_address in mappings.items():
                if source_col in row:
                    sheet[cell_address] = row[source_col]
            workbook.save(save_path)
            QMessageBox.information(self, "Success", f"Single report saved to:\n{save_path}")
        except Exception as e:
            QMessageBox.critical(self, "Save Error", f"Failed to generate single report:\n{e}")

class ManageDatabaseDialog(QDialog):
    def __init__(self, main_window, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Manage Integral Database")
        self.setMinimumSize(700, 450)
        self.main_window = main_window
        self.db_merge_config = self.main_window.database_merge_config.copy()
        
        self.layout = QVBoxLayout(self)
        self.layout.addWidget(QLabel("<b>1. Select folders and their extraction profiles to include in the Database:</b>"))
        self.folder_table = QTableWidget()
        self.folder_table.setColumnCount(3)
        self.folder_table.setHorizontalHeaderLabels(["Include", "Folder Path", "PDF Extraction Profile"])
        self.folder_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
        self.populate_table()
        self.layout.addWidget(self.folder_table)

        merge_layout = QHBoxLayout()
        merge_layout.addWidget(QLabel("<b>2. Configure how to merge data across all folders for the database:</b>"))
        self.btn_merge_config = QPushButton("Configure Merge...")
        self.btn_merge_config.clicked.connect(self.configure_merge)
        self.lbl_merge_status = QLabel()
        self.update_merge_status_label()
        merge_layout.addWidget(self.btn_merge_config)
        merge_layout.addWidget(self.lbl_merge_status)
        merge_layout.addStretch()
        self.layout.addLayout(merge_layout)

        self.btn_refresh = QPushButton("Refresh Database (Index)")
        self.btn_refresh.setToolTip("Removes all data from selected folders and re-processes them to update the database.")
        self.btn_refresh.clicked.connect(self.refresh_database)
        self.layout.addWidget(self.btn_refresh)
        
        self.button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        self.button_box.accepted.connect(self.save_and_close)
        self.button_box.rejected.connect(self.reject)
        self.layout.addWidget(self.button_box)

    def populate_table(self):
        db_config = self.main_window.database_folders_config
        all_folders = {self.main_window.folder_table.item(r, 0).text(): self.main_window.folder_table.cellWidget(r, 1).currentText() for r in range(self.main_window.folder_table.rowCount())}
        self.folder_table.setRowCount(len(all_folders))
        profile_list = self.main_window.get_profile_list()

        for i, (path, default_profile) in enumerate(all_folders.items()):
            chk = QTableWidgetItem()
            chk.setFlags(Qt.ItemIsUserCheckable | Qt.ItemIsEnabled)
            chk.setCheckState(Qt.Checked if path in db_config else Qt.Unchecked)
            self.folder_table.setItem(i, 0, chk)
            self.folder_table.setItem(i, 1, QTableWidgetItem(path))
            combo = QComboBox()
            combo.addItems(profile_list)
            combo.setCurrentText(db_config.get(path, default_profile))
            self.folder_table.setCellWidget(i, 2, combo)

    def get_selected_config(self):
        config = {}
        for row in range(self.folder_table.rowCount()):
            if self.folder_table.item(row, 0).checkState() == Qt.Checked:
                path = self.folder_table.item(row, 1).text()
                profile = self.folder_table.cellWidget(row, 2).currentText()
                if profile == "<Select Profile>":
                    QMessageBox.warning(self, "Profile Missing", f"Please select an extraction profile for folder:\n{path}")
                    return None
                config[path] = profile
        return config

    def configure_merge(self):
        QMessageBox.information(self, "Info", "First, all PDFs from checked folders will be processed to determine available columns for merging.")
        folder_config = self.get_selected_config()
        if folder_config is None: return
        raw_df = self.main_window.get_raw_data_for_db_folders(folder_config)
        if raw_df is None or raw_df.empty:
            QMessageBox.warning(self, "No Data", "No data could be extracted from the selected folders. Cannot configure merge.")
            return
        dlg = MergeConfigDialog(raw_df, raw_df.columns.tolist(), self, existing_config=self.db_merge_config)
        if dlg.exec_():
            self.db_merge_config = dlg.get_config()
            self.update_merge_status_label()

    def update_merge_status_label(self):
        self.lbl_merge_status.setText("Configured" if self.db_merge_config else "Not configured")

    def refresh_database(self):
        folder_config = self.get_selected_config()
        if folder_config is None: return
        if not folder_config:
            QMessageBox.information(self, "No Folders", "Please check folders to include in the database.")
            return
        if not self.db_merge_config:
            QMessageBox.warning(self, "Merge Not Configured", "Please configure the merge settings for the database first.")
            return
        
        reply = QMessageBox.question(self, "Confirm Refresh", f"This will re-process PDFs in {len(folder_config)} folder(s) and update the database. Continue?", QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        if reply == QMessageBox.Yes:
            self.main_window.rebuild_database(folder_config, self.db_merge_config)

    def save_and_close(self):
        folder_config = self.get_selected_config()
        if folder_config is not None:
            self.main_window.database_folders_config = folder_config
            self.main_window.database_merge_config = self.db_merge_config
            self.main_window.save_app_config()
            self.accept()

# --- END: Bulk Report Generation & Database Classes ---

# In file: report.py

class MainWindow(QMainWindow):
    # --- ENHANCED RESPONSIVE MAIN WINDOW ---
    def __init__(self):
        super().__init__()
        self.setWindowTitle("PDF Table Extractor v5.6 (Responsive Edition)")

        # Set responsive sizing instead of fixed minimum
        self.setMinimumSize(320, 480)  # Mobile-friendly minimum
        self.resize(1200, 800)  # Default desktop size

        # Enable responsive sizing policies
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        # Apply responsive stylesheet
        if RESPONSIVE_UI_AVAILABLE:
            self.setStyleSheet(apply_responsive_stylesheet())

        # Central widget and layout setup with responsive container
        if RESPONSIVE_UI_AVAILABLE:
            self.cw = ResponsiveWidget()
            self.layout = ResponsiveLayout(self.cw)
        else:
            self.cw = QWidget()
            self.layout = QVBoxLayout(self.cw)

        self.setCentralWidget(self.cw)

        # Attribute initialization
        self.all_dfs = []
        self.database_folders_config = {}
        self.database_merge_config = {}
        self.summary_tool_config = {} # Attribute is now correctly initialized

        # UI and config loading
        self._setup_responsive_ui()
        self.load_app_config()
        self.update_all_profile_combos()

        # Connect to screen changes for responsiveness
        if RESPONSIVE_UI_AVAILABLE and QApplication.instance():
            screen = QApplication.instance().primaryScreen()
            if screen:
                screen.geometryChanged.connect(self.on_screen_changed)

    def on_screen_changed(self):
        """Handle screen geometry changes"""
        if hasattr(self.cw, 'update_responsive_layout'):
            self.cw.update_responsive_layout()

    # --- THE REST OF THE METHODS ARE NOW CORRECT ---

    def open_reports_summary(self):
        # The callback allows the dialog to trigger a save in the main window
        dlg = ReportsSummaryDialog(self, config=self.summary_tool_config, config_save_callback=self.save_app_config)
        dlg.exec_()

    def _setup_responsive_ui(self):
        """Setup responsive UI with enhanced components"""
        # Create main scroll container for mobile compatibility
        if RESPONSIVE_UI_AVAILABLE:
            main_container = ScrollableContainer()
            self.layout.addWidget(main_container)
            content_layout = main_container.content_layout
        else:
            content_layout = self.layout

        # Top toolbar with responsive layout
        top_layout = QHBoxLayout()
        self.btn_create_profile = QPushButton("Create New Profile")
        self.btn_create_profile.clicked.connect(lambda: self.edit_profile(None))
        top_layout.addWidget(self.btn_create_profile)

        self.btn_edit_profile = QPushButton("Edit Existing Profile...")
        self.btn_edit_profile.clicked.connect(self.select_profile_to_edit)
        top_layout.addWidget(self.btn_edit_profile)

        self.btn_manage_profiles = QPushButton("Manage Profiles & Cache...")
        self.btn_manage_profiles.clicked.connect(self.manage_profiles)
        top_layout.addWidget(self.btn_manage_profiles)
        top_layout.addStretch()

        # Create responsive toolbar container
        toolbar_widget = QWidget()
        toolbar_widget.setLayout(top_layout)
        content_layout.addWidget(toolbar_widget)

        # Setup main content areas with responsive splitter
        if RESPONSIVE_UI_AVAILABLE:
            self.main_splitter = ResponsiveSplitter()
            content_layout.addWidget(self.main_splitter)

            # Left panel for main controls
            left_panel = ResponsiveWidget()
            left_layout = ResponsiveLayout(left_panel)
            self.main_splitter.addWidget(left_panel)

            # Right panel for results (will be hidden on mobile)
            right_panel = ResponsiveWidget()
            right_layout = ResponsiveLayout(right_panel)
            self.main_splitter.addWidget(right_panel)

            # Setup content in panels
            self._setup_responsive_single_file_group(left_layout)
            self._setup_responsive_batch_group(left_layout)
            self._setup_responsive_tools_group(left_layout)
            self._setup_responsive_results_group(right_layout)
        else:
            # Fallback to original layout
            self._setup_single_file_group()
            self._setup_batch_group()
            self._setup_tools_group()
            self._setup_results_group()

    def _setup_responsive_single_file_group(self, parent_layout):
        """Setup responsive single file processing group"""
        single_group = QGroupBox("Single File Processing")
        single_layout = QVBoxLayout(single_group)

        # File selector with responsive design
        if RESPONSIVE_UI_AVAILABLE:
            self.file_selector = ResponsiveFileSelector(file_filter="PDF Files (*.pdf)")
            self.file_selector.file_selected.connect(self.on_pdf_file_selected)
            single_layout.addWidget(self.file_selector)
        else:
            # Fallback to original layout
            sf_ctrl_layout = QHBoxLayout()
            self.btn_select_pdf = QPushButton("Select PDF File...")
            self.btn_select_pdf.clicked.connect(self.process_single_file)
            sf_ctrl_layout.addWidget(self.btn_select_pdf)
            self.single_file_label = QLabel("No file selected.")
            sf_ctrl_layout.addWidget(self.single_file_label, 1)
            single_layout.addLayout(sf_ctrl_layout)

        # Action buttons
        action_layout = QHBoxLayout()
        self.btn_process_single = QPushButton("Process File")
        self.btn_process_single.clicked.connect(self.process_single_file)
        self.btn_process_single.setEnabled(False)
        action_layout.addWidget(self.btn_process_single)

        self.btn_save_single = QPushButton("Save Result to Excel")
        self.btn_save_single.clicked.connect(self.save_single_result)
        self.btn_save_single.setEnabled(False)
        action_layout.addWidget(self.btn_save_single)

        single_layout.addLayout(action_layout)
        parent_layout.addWidget(single_group)

    def on_pdf_file_selected(self, file_path):
        """Handle PDF file selection"""
        self.selected_pdf_path = file_path
        self.btn_process_single.setEnabled(True)

    def _setup_single_file_group(self):
        """Original single file group setup for fallback"""
        single_group = QGroupBox("Single File Processing")
        single_layout = QVBoxLayout(single_group)
        sf_ctrl_layout = QHBoxLayout()
        self.btn_select_pdf = QPushButton("Select PDF File...")
        self.btn_select_pdf.clicked.connect(self.process_single_file)
        sf_ctrl_layout.addWidget(self.btn_select_pdf)
        self.single_file_label = QLabel("No file selected.")
        sf_ctrl_layout.addWidget(self.single_file_label, 1)
        self.btn_save_single = QPushButton("Save Result to Excel")
        self.btn_save_single.clicked.connect(self.save_single_result)
        self.btn_save_single.setEnabled(False)
        sf_ctrl_layout.addWidget(self.btn_save_single)
        single_layout.addLayout(sf_ctrl_layout)
        self.layout.addWidget(single_group)

    def _setup_responsive_batch_group(self, parent_layout):
        """Setup responsive batch processing group"""
        batch_group = QGroupBox("Batch Processing")
        batch_layout = QVBoxLayout(batch_group)

        # Folder management controls
        folder_layout = QHBoxLayout()
        self.btn_add_folder = QPushButton("Add Folder")
        self.btn_add_folder.clicked.connect(self.add_folder)
        folder_layout.addWidget(self.btn_add_folder)
        self.btn_remove_folder = QPushButton("Remove Selected")
        self.btn_remove_folder.clicked.connect(self.remove_folder)
        folder_layout.addWidget(self.btn_remove_folder)
        folder_layout.addStretch()
        batch_layout.addLayout(folder_layout)

        # Enhanced folder table with Excel-like features
        if RESPONSIVE_UI_AVAILABLE:
            self.folder_table = EnhancedDataTable(auto_save_file="folder_config_autosave.json")
            # Add validation rules
            self.folder_table.add_validation_rule(0, 'required', error_message="Folder path is required")
            self.folder_table.add_validation_rule(1, 'required', error_message="Profile selection is required")
        else:
            self.folder_table = QTableWidget()

        self.folder_table.setColumnCount(2)
        self.folder_table.setHorizontalHeaderLabels(["Folder Path", "Profile"])
        self.folder_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)
        self.folder_table.setSelectionBehavior(QTableWidget.SelectRows)
        batch_layout.addWidget(self.folder_table)

        # Processing controls
        process_ctrl_layout = QHBoxLayout()
        process_ctrl_layout.addStretch()
        self.btn_proc_sel_folder = QPushButton("Process Selected & Save")
        self.btn_proc_sel_folder.clicked.connect(self.process_selected_folders)
        process_ctrl_layout.addWidget(self.btn_proc_sel_folder)
        self.btn_proc_batch = QPushButton("Process All Folders")
        self.btn_proc_batch.clicked.connect(self.process_all_folders)
        process_ctrl_layout.addWidget(self.btn_proc_batch)
        batch_layout.addLayout(process_ctrl_layout)

        # Enhanced progress widget
        if RESPONSIVE_UI_AVAILABLE:
            self.progress_widget = EnhancedProgressWidget()
            batch_layout.addWidget(self.progress_widget)
        else:
            self.progress_bar = QProgressBar()
            batch_layout.addWidget(self.progress_bar)

        parent_layout.addWidget(batch_group)

    def _setup_batch_group(self):
        """Original batch group setup for fallback"""
        batch_group = QGroupBox("Batch Processing")
        batch_layout = QVBoxLayout(batch_group)
        folder_layout = QHBoxLayout()
        self.btn_add_folder = QPushButton("Add Folder")
        self.btn_add_folder.clicked.connect(self.add_folder)
        folder_layout.addWidget(self.btn_add_folder)
        self.btn_remove_folder = QPushButton("Remove Selected")
        self.btn_remove_folder.clicked.connect(self.remove_folder)
        folder_layout.addWidget(self.btn_remove_folder)
        folder_layout.addStretch()
        batch_layout.addLayout(folder_layout)

        self.folder_table = QTableWidget()
        self.folder_table.setColumnCount(2)
        self.folder_table.setHorizontalHeaderLabels(["Folder Path", "Profile"])
        self.folder_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)
        self.folder_table.setSelectionBehavior(QTableWidget.SelectRows)
        batch_layout.addWidget(self.folder_table)

        process_ctrl_layout = QHBoxLayout()
        process_ctrl_layout.addStretch()
        self.btn_proc_sel_folder = QPushButton("Process Selected & Save")
        self.btn_proc_sel_folder.clicked.connect(self.process_selected_folders)
        process_ctrl_layout.addWidget(self.btn_proc_sel_folder)
        self.btn_proc_batch = QPushButton("Process All Folders")
        self.btn_proc_batch.clicked.connect(self.process_all_folders)
        process_ctrl_layout.addWidget(self.btn_proc_batch)
        batch_layout.addLayout(process_ctrl_layout)

        self.progress_bar = QProgressBar()
        batch_layout.addWidget(self.progress_bar)
        self.layout.addWidget(batch_group)

    def _setup_responsive_tools_group(self, parent_layout):
        """Setup responsive tools group"""
        tools_group = QGroupBox("Advanced Tools")
        tools_layout = QVBoxLayout(tools_group)  # Use vertical layout for better mobile experience

        # Create responsive button layout
        button_layout = QHBoxLayout()

        self.btn_summary_tool = QPushButton("Reports Summary...")
        self.btn_summary_tool.clicked.connect(self.open_reports_summary)
        button_layout.addWidget(self.btn_summary_tool)

        self.btn_bulk_report = QPushButton("Bulk Report Tool...")
        self.btn_bulk_report.clicked.connect(self.open_bulk_report_tool)
        if not OPENPYXL_AVAILABLE:
            self.btn_bulk_report.setEnabled(False)
            self.btn_bulk_report.setToolTip("Required library 'openpyxl' not found.")
        button_layout.addWidget(self.btn_bulk_report)

        self.btn_manage_db = QPushButton("Manage Database...")
        self.btn_manage_db.clicked.connect(self.manage_database)
        if not OPENPYXL_AVAILABLE:
            self.btn_manage_db.setEnabled(False)
            self.btn_manage_db.setToolTip("Required library 'openpyxl' not found.")
        button_layout.addWidget(self.btn_manage_db)

        tools_layout.addLayout(button_layout)
        parent_layout.addWidget(tools_group)

    def _setup_tools_group(self):
        """Original tools group setup for fallback"""
        tools_group = QGroupBox("Advanced Tools")
        tools_layout = QHBoxLayout(tools_group)

        self.btn_summary_tool = QPushButton("Reports Summary...")
        self.btn_summary_tool.clicked.connect(self.open_reports_summary)
        tools_layout.addWidget(self.btn_summary_tool)

        self.btn_bulk_report = QPushButton("Bulk Report Tool...")
        self.btn_bulk_report.clicked.connect(self.open_bulk_report_tool)
        if not OPENPYXL_AVAILABLE:
            self.btn_bulk_report.setEnabled(False)
            self.btn_bulk_report.setToolTip("Required library 'openpyxl' not found.")
        tools_layout.addWidget(self.btn_bulk_report)

        self.btn_manage_db = QPushButton("Manage Database...")
        self.btn_manage_db.clicked.connect(self.manage_database)
        if not OPENPYXL_AVAILABLE:
            self.btn_manage_db.setEnabled(False)
            self.btn_manage_db.setToolTip("Required library 'openpyxl' not found.")
        tools_layout.addWidget(self.btn_manage_db)
        self.layout.addWidget(tools_group)

    def _setup_responsive_results_group(self, parent_layout):
        """Setup responsive results group"""
        results_group = QGroupBox("Results")
        results_layout = QVBoxLayout(results_group)

        # Configuration section
        config_layout = QVBoxLayout()

        # Merge configuration
        merge_layout = QHBoxLayout()
        self.btn_configure_merge = QPushButton("Configure Merge & Save...")
        self.btn_configure_merge.clicked.connect(self.configure_merge_and_save)
        self.btn_configure_merge.setEnabled(False)
        merge_layout.addWidget(self.btn_configure_merge)
        config_layout.addLayout(merge_layout)

        # Saved configurations
        saved_config_layout = QVBoxLayout()
        saved_config_layout.addWidget(QLabel("Use Saved Merge Config:"))
        self.combo_merge_configs = QComboBox()
        saved_config_layout.addWidget(self.combo_merge_configs)

        self.btn_save_with_config = QPushButton("Save with Selected Config")
        self.btn_save_with_config.clicked.connect(self.save_with_selected_config)
        self.btn_save_with_config.setEnabled(False)
        saved_config_layout.addWidget(self.btn_save_with_config)

        config_layout.addLayout(saved_config_layout)
        results_layout.addLayout(config_layout)

        # Status section
        self.status_label = QLabel("Status: Ready.")
        self.status_label.setWordWrap(True)  # Enable word wrap for long status messages
        results_layout.addWidget(self.status_label)

        parent_layout.addWidget(results_group)

    def _setup_results_group(self):
        """Original results group setup for fallback"""
        results_group = QGroupBox("Results")
        results_layout = QVBoxLayout(results_group)
        save_layout = QHBoxLayout()
        self.btn_configure_merge = QPushButton("Configure Merge & Save...")
        self.btn_configure_merge.clicked.connect(self.configure_merge_and_save)
        self.btn_configure_merge.setEnabled(False)
        save_layout.addWidget(self.btn_configure_merge)
        save_layout.addWidget(QLabel("Use Saved Merge Config:"))
        self.combo_merge_configs = QComboBox()
        save_layout.addWidget(self.combo_merge_configs)
        self.btn_save_with_config = QPushButton("Save with Selected Config")
        self.btn_save_with_config.clicked.connect(self.save_with_selected_config)
        self.btn_save_with_config.setEnabled(False)
        save_layout.addWidget(self.btn_save_with_config)
        results_layout.addLayout(save_layout)
        self.status_label = QLabel("Status: Ready.")
        results_layout.addWidget(self.status_label)
        self.layout.addWidget(results_group)

    def open_bulk_report_tool(self):
        dlg = BulkReportDialog(self)
        dlg.exec_()
        
    def manage_database(self):
        dlg = ManageDatabaseDialog(self)
        dlg.exec_()

    def closeEvent(self, event):
        self.save_app_config()
        super().closeEvent(event)

    def load_app_config(self):
        path = os.path.join(script_base_dir, APP_CONFIG_FILE)
        if not os.path.exists(path): return
        try:
            with open(path, 'r') as f: config = json.load(f)
            for folder_path, profile_name in config.get("folders", []):
                if os.path.isdir(folder_path):
                    self.add_folder(folder_path, profile_name)
            self.database_folders_config = config.get("database_folders_config", {})
            self.database_merge_config = config.get("database_merge_config", {})
            self.summary_tool_config = config.get("summary_tool_config", {})
        except Exception as e:
            print(f"Error loading app config: {e}")

    def save_app_config(self):
        config = {
            "folders": [],
            "database_folders_config": self.database_folders_config,
            "database_merge_config": self.database_merge_config,
            "summary_tool_config": self.summary_tool_config
        }
        for row in range(self.folder_table.rowCount()):
            config["folders"].append((self.folder_table.item(row, 0).text(), self.folder_table.cellWidget(row, 1).currentText()))
        try:
            with open(os.path.join(script_base_dir, APP_CONFIG_FILE), 'w') as f:
                json.dump(config, f, indent=4)
        except Exception as e:
            print(f"Error saving app config: {e}")
    
    # ... (the rest of the MainWindow methods remain the same) ...
    # (manage_profiles, select_profile_to_edit, etc.)

# The code for the other classes (Canvas, DraggableListWidget, etc.)
# should follow here, as they were in your original file.

# Remember to keep the `if __name__ == "__main__":` block at the very end.
    def manage_profiles(self):
        dlg = ManageProfilesDialog(self)
        dlg.exec_()
        self.update_all_profile_combos()

    def select_profile_to_edit(self):
        dlg = SelectProfileDialog(self)
        if dlg.exec_() and dlg.selected_profile_path:
            self.edit_profile(dlg.selected_profile_path)
            
    def edit_profile(self, path):
        dlg = CalibrationDialog(self, profile_path=path)
        dlg.exec_()
        self.update_all_profile_combos()

    def get_profile_list(self):
        return ["<Select Profile>"] + sorted([os.path.splitext(p)[0] for p in os.listdir(PROFILES_DIR) if p.endswith('.json')])
    
    def update_all_profile_combos(self):
        profiles = self.get_profile_list()
        for row in range(self.folder_table.rowCount()):
            combo = self.folder_table.cellWidget(row, 1)
            if combo:
                current_selection = combo.currentText()
                combo.clear()
                combo.addItems(profiles)
                if current_selection in profiles:
                    combo.setCurrentText(current_selection)

    def add_folder(self, folder_path=None, profile_name=None):
        if not folder_path:
            folder_path = QFileDialog.getExistingDirectory(self, "Select Folder")
        if not folder_path: return
        for row in range(self.folder_table.rowCount()):
            if self.folder_table.item(row, 0).text() == folder_path: return
        row_pos = self.folder_table.rowCount()
        self.folder_table.insertRow(row_pos)
        self.folder_table.setItem(row_pos, 0, QTableWidgetItem(folder_path))
        combo_profiles = QComboBox()
        combo_profiles.addItems(self.get_profile_list())
        self.folder_table.setCellWidget(row_pos, 1, combo_profiles)
        if profile_name:
            combo_profiles.setCurrentText(profile_name)
    
    def remove_folder(self):
        for row in sorted([index.row() for index in self.folder_table.selectedIndexes()], reverse=True):
            path = self.folder_table.item(row, 0).text()
            if path in self.database_folders_config:
                self.database_folders_config.pop(path)
            self.folder_table.removeRow(row)

    def process_single_file(self):
        pdf_path, _ = QFileDialog.getOpenFileName(self, "Select PDF File", "", "PDF Files (*.pdf)")
        if not pdf_path: return
        profile_dlg = SelectProfileDialog(self)
        if not profile_dlg.exec_() or not profile_dlg.selected_profile_path: return
        
        self.single_file_label.setText(f"Processing: {os.path.basename(pdf_path)}")
        QApplication.processEvents()
        self.reset_results()
        df, _ = self.process_file(pdf_path, profile_dlg.selected_profile_path, os.path.dirname(pdf_path))
        if df is not None:
            self.all_dfs.append(df)
        
        self.status_label.setText(f"Processing complete for {os.path.basename(pdf_path)}.")
        self.finalize_results()
        if self.all_dfs:
            self.btn_save_single.setEnabled(True)
            QMessageBox.information(self, "Success", f"Extracted data from {os.path.basename(pdf_path)}.\nResults are ready to save.")
        self.single_file_label.setText("No file selected.")

    def save_single_result(self):
        if not self.all_dfs:
            QMessageBox.warning(self, "No Data", "No data to save.")
            return
        path, _ = QFileDialog.getSaveFileName(self, "Save Excel", "", "Excel Files (*.xlsx)")
        if not path: return
        try:
            final_df = self._get_final_df()
            if final_df.empty:
                QMessageBox.warning(self, "Empty Result", "Processing resulted in an empty dataset.")
                return
            final_df.to_excel(path, sheet_name="Extracted_Data", index=False)
            QMessageBox.information(self, "Saved", f"Data saved to {path}")
        except Exception as e:
            QMessageBox.critical(self, "Save Error", f"An error occurred while saving: {e}\n{traceback.format_exc()}")

    def process_selected_folders(self):
        selected_rows = sorted(list(set(index.row() for index in self.folder_table.selectedIndexes())))
        if not selected_rows:
            QMessageBox.warning(self, "No Selection", "Please select folders to process.")
            return
        tasks = []
        for row in selected_rows:
            folder_path = self.folder_table.item(row, 0).text()
            profile_name = self.folder_table.cellWidget(row, 1).currentText()
            if profile_name == "<Select Profile>":
                QMessageBox.warning(self, "Profile Not Selected", f"Select a profile for:\n{folder_path}")
                return
            profile_path = os.path.join(PROFILES_DIR, f"{profile_name}.json")
            for filename in os.listdir(folder_path):
                if filename.lower().endswith('.pdf'):
                    tasks.append((os.path.join(folder_path, filename), profile_path, os.path.basename(folder_path)))
        if not tasks:
            QMessageBox.information(self, "No Files", "No PDF files found.")
            return
            
        self.reset_results()
        self.progress_bar.setRange(0, len(tasks))
        for i, (pdf_path, profile_path, folder_name) in enumerate(tasks):
            self.status_label.setText(f"Processing {os.path.basename(pdf_path)} ({i+1}/{len(tasks)})...")
            self.progress_bar.setValue(i + 1)
            QApplication.processEvents()
            df, _ = self.process_file(pdf_path, profile_path, folder_name)
            if df is not None:
                self.all_dfs.append(df)
        
        self.status_label.setText(f"Processing of {len(selected_rows)} folder(s) complete.")
        self.finalize_results()
        if self.all_dfs:
            self.save_results_to_excel()

    def save_results_to_excel(self):
        if not self.all_dfs:
            QMessageBox.warning(self, "No Data", "No data to save.")
            return
        path, _ = QFileDialog.getSaveFileName(self, "Save Merged Excel File", "", "Excel Files (*.xlsx)")
        if not path: return
        final_df = self._get_final_df()
        if final_df.empty:
            QMessageBox.warning(self, "Empty Result", "Processing resulted in an empty dataset.")
            return
        try:
            self.status_label.setText(f"Saving to Excel...")
            QApplication.processEvents()
            final_df.to_excel(path, sheet_name="Extracted_Data", index=False)
            QMessageBox.information(self, "Saved", f"Data saved to {path}")
            self.status_label.setText("Ready.")
        except Exception as e:
            QMessageBox.critical(self, "Save Error", f"An error occurred saving data: {e}\n{traceback.format_exc()}")

    def process_all_folders(self):
        if self.folder_table.rowCount() == 0:
            QMessageBox.warning(self, "No Folders", "Please add at least one folder.")
            return
        tasks = []
        for row in range(self.folder_table.rowCount()):
            folder_path = self.folder_table.item(row, 0).text()
            profile_name = self.folder_table.cellWidget(row, 1).currentText()
            if profile_name == "<Select Profile>":
                QMessageBox.warning(self, "Profile Not Selected", f"Select a profile for:\n{folder_path}")
                return
            profile_path = os.path.join(PROFILES_DIR, f"{profile_name}.json")
            for filename in os.listdir(folder_path):
                if filename.lower().endswith('.pdf'):
                    tasks.append((os.path.join(folder_path, filename), profile_path, os.path.basename(folder_path)))
        if not tasks:
            QMessageBox.information(self, "No Files", "No PDF files found.")
            return
        
        self.reset_results()
        self.progress_bar.setRange(0, len(tasks))
        for i, (pdf_path, profile_path, folder_name) in enumerate(tasks):
            self.status_label.setText(f"Processing {os.path.basename(pdf_path)} ({i+1}/{len(tasks)})...")
            self.progress_bar.setValue(i + 1)
            QApplication.processEvents()
            df, _ = self.process_file(pdf_path, profile_path, folder_name)
            if df is not None:
                self.all_dfs.append(df)
        self.status_label.setText(f"Batch processing complete. Found data in {len(self.all_dfs)} pages.")
        self.finalize_results()

    def reset_results(self):
        self.all_dfs = []
        self.btn_configure_merge.setEnabled(False)
        self.btn_save_with_config.setEnabled(False)
        self.combo_merge_configs.clear()
        self.progress_bar.setValue(0)
        self.btn_save_single.setEnabled(False)
    
    def _get_final_df(self):
        if not self.all_dfs: return pd.DataFrame()
        try:
            return pd.concat(self.all_dfs, ignore_index=True)
        except (ValueError, pd.errors.InvalidIndexError) as e:
            QMessageBox.warning(self, "Column Mismatch", f"Could not combine results. Check profile consistency.\nError: {e}")
            return pd.DataFrame()

    def finalize_results(self):
        if self.all_dfs:
            final_df = self._get_final_df()
            if not final_df.empty:
                self.btn_configure_merge.setEnabled(True)
                self.update_merge_configs_combo()
            else:
                self.btn_configure_merge.setEnabled(False)
                self.btn_save_with_config.setEnabled(False)
        else:
            self.status_label.setText("Processing complete. No data extracted.")
            QMessageBox.warning(self, "No Data", "No data could be extracted from the file(s) using the selected profile(s).")
    
    def update_merge_configs_combo(self):
        self.combo_merge_configs.clear()
        configs = sorted([os.path.splitext(p)[0] for p in os.listdir(MERGE_CONFIGS_DIR) if p.endswith('.json')])
        if configs:
            self.combo_merge_configs.addItems(configs)
            self.btn_save_with_config.setEnabled(True)
        else:
            self.btn_save_with_config.setEnabled(False)

    def get_cache_key(self, pdf_path, profile_path):
        try:
            return hashlib.md5(f"{pdf_path}{os.path.getmtime(pdf_path)}{profile_path}{os.path.getmtime(profile_path)}".encode()).hexdigest()
        except OSError:
            return None

    def process_file(self, pdf_path, profile_path, folder_name):
        cache_key = self.get_cache_key(pdf_path, profile_path)
        cache_file_path = os.path.join(cache_full_path, f"{cache_key}.json")
        
        if cache_key and os.path.exists(cache_file_path):
            try:
                return pd.read_json(cache_file_path, orient='split'), True
            except Exception as e: 
                print(f"Cache read failed for {cache_key}, processing normally. Error: {e}")
        try:
            with open(profile_path, 'r') as f: cfg = json.load(f)
            origin_text = cfg.get("regions", {}).get("origin", {}).get("text")
            if not origin_text:
                return None, False
            use_text_layer = cfg.get("is_text_based", False) and PDFPLUMBER_AVAILABLE
            file_dfs = []
            with pdfplumber.open(pdf_path) as pdf: 
                for page_num in range(len(pdf.pages)):
                    origin_coords = self.find_origin_with_pdfplumber(pdf_path, page_num, origin_text) if use_text_layer else self._find_origin_with_ocr_from_path(pdf_path, page_num, origin_text, cfg.get("enhancement_config"))
                    if origin_coords:
                        page_df = self._process_single_page_with_origin(pdf_path, page_num, cfg, origin_coords, folder_name)
                        if not page_df.empty:
                            file_dfs.append(page_df)
            if file_dfs:
                consolidated_df = pd.concat(file_dfs, ignore_index=True)
                if cache_key:
                    consolidated_df.to_json(cache_file_path, orient='split', index=False)
                return consolidated_df, False
            return None, False
        except Exception as e:
            print(f"Failed to process {pdf_path}: {e}\n{traceback.format_exc()}")
            return None, False

    def _find_origin_with_ocr_from_path(self, pdf_path, page_num, text, enhancement_config):
        img = convert_from_path(pdf_path, dpi=300, first_page=page_num + 1, last_page=page_num + 1, poppler_path=current_poppler_path_for_pdf2image)[0]
        processed_img = self.apply_image_enhancements(img, enhancement_config)
        p_data = pytesseract.image_to_data(processed_img, output_type=pytesseract.Output.DICT)
        o_words = text.split()
        for j in range(len(p_data['text']) - len(o_words) + 1):
            sequence = [word for word in p_data['text'][j : j + len(o_words)] if word.strip()]
            if sequence == o_words:
                w, h = processed_img.size
                return (p_data['left'][j] / w, p_data['top'][j] / h)
        return None

    def get_raw_data_for_db_folders(self, folder_config):
        all_dfs = []
        tasks = [(os.path.join(path, fn), os.path.join(PROFILES_DIR, f"{p_name}.json"), os.path.basename(path)) for path, p_name in folder_config.items() for fn in os.listdir(path) if fn.lower().endswith('.pdf')]
        if not tasks: return pd.DataFrame()

        progress = QProgressDialog("Gathering data...", "Cancel", 0, len(tasks), self)
        progress.setWindowModality(Qt.WindowModal)
        for i, (pdf_path, profile_path, folder_name) in enumerate(tasks):
            progress.setValue(i)
            if progress.wasCanceled(): break
            df, was_cached = self.process_file(pdf_path, profile_path, folder_name)
            progress.setLabelText(f"{'Cached' if was_cached else 'Processing'}: {os.path.basename(pdf_path)}")
            if df is not None: all_dfs.append(df)
        progress.setValue(len(tasks))
        return pd.concat(all_dfs, ignore_index=True) if all_dfs else pd.DataFrame()
        
    def rebuild_database(self, folder_config, merge_config):
        self.status_label.setText("Rebuilding database...")
        QApplication.processEvents()
        
        db_df = pd.DataFrame()
        try:
            if os.path.exists(integral_database_path):
                db_df = pd.read_excel(integral_database_path)
            folder_names_to_remove = [os.path.basename(p) for p in folder_config.keys()]
            if 'Source_Folder' in db_df.columns:
                db_df = db_df[~db_df['Source_Folder'].isin(folder_names_to_remove)]
        except Exception as e:
            QMessageBox.critical(self, "DB Read Error", f"Could not read existing database. Aborting.\nError: {e}")
            return

        raw_df = self.get_raw_data_for_db_folders(folder_config)
        
        final_df = db_df
        if not raw_df.empty:
            merged_new_df = self.perform_merge(raw_df, merge_config)
            final_df = pd.concat([db_df, merged_new_df], ignore_index=True).drop_duplicates()
        
        try:
            final_df.to_excel(integral_database_path, index=False)
            QMessageBox.information(self, "Success", "Integral Database has been successfully refreshed.")
        except Exception as e:
            QMessageBox.critical(self, "DB Save Error", f"Could not save the updated database.\nError: {e}")
        self.status_label.setText("Status: Ready.")

    def get_profile_for_folder(self, folder_path):
        for row in range(self.folder_table.rowCount()):
            if self.folder_table.item(row, 0).text() == folder_path:
                return self.folder_table.cellWidget(row, 1).currentText()
        return None
        
    @staticmethod
    def apply_image_enhancements(img, cfg):
        if not cfg or not isinstance(cfg, dict): return img
        i = img.copy()
        if cfg.get("to_greyscale", True): i = i.convert('L')
        if cfg.get("brightness", 1.0) != 1.0: i = ImageEnhance.Brightness(i).enhance(cfg["brightness"])
        if cfg.get("contrast", 1.0) != 1.0: i = ImageEnhance.Contrast(i).enhance(cfg["contrast"])
        if cfg.get("apply_median", False): i = i.filter(ImageFilter.MedianFilter(size=3))
        if cfg.get("apply_sharpen", False): i = i.filter(ImageFilter.SHARPEN)
        method = cfg.get("threshold_method", "adaptive")
        if method == "adaptive" and SCIKIT_AVAILABLE:
            bs = cfg.get("adaptive_block_size", 55);
            if bs % 2 == 0: bs += 1
            npi = np.array(i.convert('L'))
            lt = threshold_local(npi, bs, offset=cfg.get("adaptive_offset", 10))
            i = Image.fromarray(img_as_ubyte(npi > lt))
        elif method == "global" or (method == "adaptive" and not SCIKIT_AVAILABLE):
            i = i.convert('L').point(lambda p: 255 if p > cfg.get("global_threshold", 180) else 0, '1')
        return i
        
    @staticmethod
    def ocr_region(img, norm_rect, psm="7", name="?"):
        if not all(isinstance(n, (int, float)) for n in norm_rect) or len(norm_rect) != 4: return ""
        w, h = img.size
        x1, y1, x2, y2 = int(norm_rect[0]*w), int(norm_rect[1]*h), int(norm_rect[2]*w), int(norm_rect[3]*h)
        if x1 >= x2 or y1 >= y2: return ""
        try:
            return pytesseract.image_to_string(img.crop((x1, y1, x2, y2)), config=f'--oem 3 --psm {psm} -l eng').strip()
        except Exception:
            return ""

    @staticmethod
    def text_layer_region(pdf_path, page_num, norm_rect, name="?"):
        try:
            with pdfplumber.open(pdf_path) as pdf:
                if page_num >= len(pdf.pages): return f"Page {page_num} out of bounds"
                page = pdf.pages[page_num]
                bbox = (norm_rect[0]*page.width, norm_rect[1]*page.height, norm_rect[2]*page.width, norm_rect[3]*page.height)
                return (page.crop(bbox).extract_text(x_tolerance=3, y_tolerance=3) or "").strip()
        except Exception as e:
            return f"pdfplumber error: {e}"
        
    @staticmethod
    def chunk_text(text, method, param):
        if not text: return []
        try:
            if method == "Smart Split": return [val for val in re.split(r'\s{2,}|[|]', text) if val.strip()]
            if method == "Newline Delimited": return [val for val in text.split('\n') if val.strip()]
            if method == "Delimiter" and param: return [c.strip() for c in text.split(param) if c.strip()]
            if method == "Fixed Width" and param > 0: return [text[i:i + param].strip() for i in range(0, len(text), param) if text[i:i + param].strip()]
            if method == "Regex" and param: return [c.strip() for c in re.split(param, text) if c.strip()]
        except Exception as e:
            return [f"CHUNK ERROR: {e}"]
        return []

    @staticmethod
    def find_origin_with_pdfplumber(pdf_path, page_num, origin_text):
        try:
            with pdfplumber.open(pdf_path) as pdf:
                if page_num < len(pdf.pages):
                    results = pdf.pages[page_num].search(origin_text, case=False, strict=False)
                    if results:
                        return (results[0]['x0'] / pdf.pages[page_num].width, results[0]['top'] / pdf.pages[page_num].height)
        except Exception as e:
            print(f"pdfplumber origin search error: {e}")
        return None

    def _process_single_page_with_origin(self, pdf_path, page_num, config, origin_coords, folder_name):
        regions = config.get("regions", {})
        use_subrows = config.get("use_subrows", True)
        pivot_subrows = config.get("pivot_subrows", False)
        use_text_layer = config.get("is_text_based", False) and PDFPLUMBER_AVAILABLE
        ox, oy = origin_coords
        processed_img_for_ocr = None
        output_rows = []

        def get_region_text(name, norm_rect):
            nonlocal processed_img_for_ocr
            if not norm_rect: return ""
            if use_text_layer: return self.text_layer_region(pdf_path, page_num, norm_rect, name)
            if processed_img_for_ocr is None:
                img = convert_from_path(pdf_path, dpi=300, first_page=page_num + 1, last_page=page_num + 1, poppler_path=current_poppler_path_for_pdf2image)[0]
                processed_img_for_ocr = self.apply_image_enhancements(img, config.get("enhancement_config"))
            psm_conf = config.get("psm_config", {})
            psm = psm_conf.get("data_blocks_psm", "6") if name.startswith("data_b") else psm_conf.get("row_col_headers_psm", "7")
            return self.ocr_region(processed_img_for_ocr, norm_rect, psm, name)

        abs_regions = {name: [data['offset_rect'][0] + ox, data['offset_rect'][1] + oy, data['offset_rect'][2] + ox, data['offset_rect'][3] + oy] for name, data in regions.items() if 'offset_rect' in data}
        col_keys = sorted([k for k in regions if k.startswith("col_h")], key=lambda x: int(re.search(r'\d+$', x).group()))
        col_headers = {k: regions[k].get("text", k) if config.get("fix_column_headers", False) else get_region_text(k, abs_regions.get(k)) for k in col_keys}

        is_column_oriented = any(data.get('link_type') == 'column' for name, data in regions.items() if name.startswith('data_b'))

        if is_column_oriented:
            if use_subrows:
                # This logic path would be for column-oriented profiles that also use subrows.
                # For now, we assume this combination is not used or we can implement it similarly.
                # The provided example `product.json` has `use_subrows: false`.
                # We will treat it as a simple row case for now.
                pass  # Fall through to the non-subrow logic for now which should handle it if keys match
            
            # This logic handles both `use_subrows: false` and can handle `use_subrows: true`
            # if the chunking_config links to `sub_row_X` keys.
            row_keys_to_process = sorted([k for k in regions if k.startswith("row_h" if not use_subrows else "sub_r")])
            records = { r_key: {} for r_key in row_keys_to_process }

            # Pre-populate headers
            if use_subrows:
                main_rows_text = {k: get_region_text(k, v) for k, v in abs_regions.items() if k.startswith("row_h")}
                for sub_key in row_keys_to_process:
                    main_row_link = regions.get(sub_key, {}).get('links_to_main_row')
                    records[sub_key]["Main_Header"] = main_rows_text.get(main_row_link, "")
                    records[sub_key]["Sub_Header"] = get_region_text(sub_key, abs_regions.get(sub_key))
            else:
                 for row_key in row_keys_to_process:
                     records[row_key]["Main_Header"] = get_region_text(row_key, abs_regions.get(row_key))

            # Process each column-linked data block
            for block_key, block_data in regions.items():
                if not block_key.startswith("data_b") or block_data.get('link_type') != 'column':
                    continue

                col_key = block_data.get('links_to_col')
                if not col_key or col_key not in col_headers:
                    continue

                col_name = col_headers[col_key]
                data_str = get_region_text(block_key, abs_regions.get(block_key))
                chunk_cfg = block_data.get('chunking_config', {})
                if not chunk_cfg: continue
                chunks = self.chunk_text(data_str, chunk_cfg.get('method'), chunk_cfg.get('param'))
                
                # Map chunks to rows for the current column
                for linked_row_key, indices in chunk_cfg.get('links', {}).items():
                    if linked_row_key in records:
                        value = " ".join([chunks[j] for j in indices if j < len(chunks)])
                        records[linked_row_key][col_name] = value
            
            output_rows = list(records.values())

        else: # Fallback to original row-oriented logic
            if use_subrows:
                main_rows_text = {k: get_region_text(k, v) for k, v in abs_regions.items() if k.startswith("row_h")}
                main_row_groups = {}
                for sub_key, sub_data in regions.items():
                    if sub_key.startswith("sub_r") and sub_data.get('links_to_main_row'):
                        main_row_groups.setdefault(sub_data.get('links_to_main_row'), []).append(sub_key)
                for main_row_key, sub_row_keys in main_row_groups.items():
                    if pivot_subrows:
                        pivoted_record = {"Main_Header": main_rows_text.get(main_row_key, "")}
                        for i, sub_row_key in enumerate(sub_row_keys, 1):
                            pivoted_record[f"Sub_Header_{i}"] = get_region_text(sub_row_key, abs_regions.get(sub_row_key))
                            for block_key, block_data in regions.items():
                                if block_data.get('links_to_sub_row') == sub_row_key:
                                    chunk_cfg = block_data.get('chunking_config')
                                    data_str = get_region_text(block_key, abs_regions.get(block_key))
                                    chunks = self.chunk_text(data_str, chunk_cfg.get('method'), chunk_cfg.get('param'))
                                    for col_key, indices in chunk_cfg.get('links', {}).items():
                                        col_name = col_headers.get(col_key)
                                        if col_name:
                                            pivoted_record[f"{col_name}_{i}"] = " ".join([chunks[j] for j in indices if j < len(chunks)])
                                    break 
                        output_rows.append(pivoted_record)
                    else:
                        for sub_row_key in sub_row_keys:
                            record = {"Main_Header": main_rows_text.get(main_row_key, ""), "Sub_Header": get_region_text(sub_row_key, abs_regions.get(sub_row_key))}
                            for block_key, block_data in regions.items():
                                if block_data.get('links_to_sub_row') == sub_row_key:
                                    chunk_cfg = block_data.get('chunking_config')
                                    data_str = get_region_text(block_key, abs_regions.get(block_key))
                                    chunks = self.chunk_text(data_str, chunk_cfg.get('method'), chunk_cfg.get('param'))
                                    for col_key, indices in chunk_cfg.get('links', {}).items():
                                        col_name = col_headers.get(col_key)
                                        if col_name:
                                            record[col_name] = " ".join([chunks[j] for j in indices if j < len(chunks)])
                                    break
                            output_rows.append(record)
            else: # Original logic for non-subrow, row-oriented profiles
                for row_key in sorted([k for k in regions if k.startswith("row_h")], key=lambda x: int(re.search(r'\d+$', x).group())):
                    record = {"Main_Header": get_region_text(row_key, abs_regions.get(row_key))}
                    for block_key, block_data in regions.items():
                        if block_data.get('links_to_row') == row_key:
                            chunk_cfg = block_data.get('chunking_config')
                            data_str = get_region_text(block_key, abs_regions.get(block_key))
                            chunks = self.chunk_text(data_str, chunk_cfg.get('method'), chunk_cfg.get('param'))
                            for col_key, indices in chunk_cfg.get('links', {}).items():
                                col_name = col_headers.get(col_key)
                                if col_name:
                                    record[col_name] = " ".join([chunks[i] for i in indices if i < len(chunks)])
                            break
                    output_rows.append(record)

        if not output_rows: return pd.DataFrame()
        page_df = pd.DataFrame(output_rows)
        page_df["Source_Folder"] = folder_name
        page_df["Source_File"] = os.path.basename(pdf_path)

        # Reorder columns to be logical
        if not pivot_subrows:
            initial_cols = ['Main_Header', 'Sub_Header'] if use_subrows else ['Main_Header']
            data_cols = [col_headers[k] for k in col_keys if col_headers.get(k) in page_df.columns]
            final_col_order = initial_cols + data_cols + ['Source_Folder', 'Source_File']
            return page_df[[c for c in final_col_order if c in page_df.columns]]
        
        # Fallback for pivoted or other structures
        return page_df[[c for c in page_df.columns if c not in ['Source_Folder', 'Source_File']] + ['Source_Folder', 'Source_File']]

    def configure_merge_and_save(self):
        if not self.all_dfs:
            QMessageBox.warning(self, "No Data", "No data to configure.")
            return
        final_df = self._get_final_df()
        if final_df.empty: return
        dlg = MergeConfigDialog(final_df, final_df.columns.tolist(), self)
        if dlg.exec_():
            self.save_with_config(dlg.get_config())

    def save_with_selected_config(self):
        config_name = self.combo_merge_configs.currentText()
        if not config_name:
            QMessageBox.warning(self, "No Selection", "Please select a merge configuration.")
            return
        try:
            with open(os.path.join(MERGE_CONFIGS_DIR, f"{config_name}.json"), 'r') as f:
                config = json.load(f)
            self.save_with_config(config)
        except Exception as e:
            QMessageBox.critical(self, "Load Error", f"Could not load merge configuration: {e}")

    @staticmethod
    def perform_merge(dataframe, config):
        if not config: return dataframe
        df = dataframe.copy()
        try:
            group_by_cols = config.get('group_by_columns', [])
            agg_config = config.get('aggregations', {})
            if not group_by_cols:
                return df[config.get('columns_to_include', df.columns)]
            for col in group_by_cols:
                if col in df.columns:
                    df[col] = df[col].astype(str).fillna('')
            agg_funcs = {}
            for col, method in agg_config.items():
                if col in df.columns:
                    if method in ["Sum", "Average", "Min Value", "Max Value"]:
                        df[col] = pd.to_numeric(df[col], errors='coerce')
                    method_map = {"Sum": 'sum', "Average": 'mean', "First Value": 'first', "Last Value": 'last', "Min Value": 'min', "Max Value": 'max'}
                    agg_funcs[col] = method_map.get(method, lambda x: ' | '.join(x.dropna().astype(str).unique()))
            
            merged_df = df.groupby(group_by_cols, as_index=False).agg(agg_funcs) if agg_funcs else df[group_by_cols].drop_duplicates()
            final_cols = [c for c in config.get('columns_to_include', []) if c in merged_df.columns]
            return merged_df[final_cols]
        except Exception as e:
            print(f"Merge error: {e}\n{traceback.format_exc()}")
            return pd.DataFrame()

    def save_with_config(self, config):
        path, _ = QFileDialog.getSaveFileName(self, "Save Excel", "", "Excel Files (*.xlsx)")
        if not path: return
        final_df = self._get_final_df()
        if final_df.empty: return
        try:
            self.status_label.setText("Merging data...")
            QApplication.processEvents()
            merged_df = self.perform_merge(final_df, config)
            merged_df.to_excel(path, sheet_name="Merged_Data", index=False)
            QMessageBox.information(self, "Saved", f"Data saved to {path}")
            self.status_label.setText("Ready.")
        except Exception as e:
            QMessageBox.critical(self, "Save Error", f"An error occurred while saving: {e}\n{traceback.format_exc()}")


if __name__ == "__main__":
    app = QApplication.instance() or QApplication(sys.argv)
    mainWin = MainWindow()
    mainWin.show()
    sys.exit(app.exec_())
#!/usr/bin/env python3
"""
Test script for Excel-like features in Report Maker v2
This script specifically tests the enhanced table functionality
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton, QHBoxLayout
from PyQt5.QtCore import Qt

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from responsive_ui import ExcelLikeTableWidget, apply_responsive_stylesheet
    from enhanced_widgets import EnhancedDataTable
    print("✓ Excel-like components imported successfully")
    EXCEL_FEATURES_AVAILABLE = True
except ImportError as e:
    print(f"✗ Failed to import Excel-like components: {e}")
    EXCEL_FEATURES_AVAILABLE = False


class ExcelFeaturesTestWindow(QMainWindow):
    """Test window for Excel-like features"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Report Maker v2 - Excel Features Test")
        self.setMinimumSize(800, 600)
        self.resize(1200, 800)
        
        if EXCEL_FEATURES_AVAILABLE:
            self.setStyleSheet(apply_responsive_stylesheet())
            self.setup_excel_test()
        else:
            self.setup_fallback()
    
    def setup_excel_test(self):
        """Setup Excel features test"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Title and instructions
        title = QLabel("Excel-like Features Test")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 18px; font-weight: bold; margin: 10px;")
        layout.addWidget(title)
        
        instructions = QLabel(
            "Test the following Excel-like features:\n"
            "• Tab/Shift+Tab: Navigate between cells\n"
            "• Enter: Move to next row\n"
            "• Ctrl+C/Ctrl+V: Copy/Paste\n"
            "• Ctrl+D: Fill Down\n"
            "• Ctrl+R: Fill Right\n"
            "• Ctrl+Shift+A: Auto-fit columns\n"
            "• Right-click: Context menu with Excel-like options\n"
            "• Type invalid data to see validation\n"
            "• Auto-save every 30 seconds"
        )
        instructions.setStyleSheet("margin: 10px; padding: 10px; background: #f0f0f0; border-radius: 5px;")
        layout.addWidget(instructions)
        
        # Test buttons
        button_layout = QHBoxLayout()
        
        self.btn_populate = QPushButton("Populate Sample Data")
        self.btn_populate.clicked.connect(self.populate_sample_data)
        button_layout.addWidget(self.btn_populate)
        
        self.btn_validate = QPushButton("Test Validation")
        self.btn_validate.clicked.connect(self.test_validation)
        button_layout.addWidget(self.btn_validate)
        
        self.btn_clear = QPushButton("Clear Table")
        self.btn_clear.clicked.connect(self.clear_table)
        button_layout.addWidget(self.btn_clear)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        # Enhanced data table
        self.table = EnhancedDataTable(auto_save_file="excel_test_autosave.json")
        self.table.setRowCount(15)
        self.table.setColumnCount(6)
        self.table.setHorizontalHeaderLabels([
            "Product Name", "Price", "Quantity", "Date", "Category", "Notes"
        ])
        
        # Add validation rules
        self.table.add_validation_rule(0, 'required', error_message="Product name is required")
        self.table.add_validation_rule(1, 'numeric', error_message="Price must be a number")
        self.table.add_validation_rule(2, 'numeric', error_message="Quantity must be a number")
        
        # Add column formatting
        self.table.add_column_format(1, 'currency')  # Price column
        self.table.add_column_format(2, 'number', {'decimals': 0})  # Quantity column
        self.table.add_column_format(3, 'date', {
            'input_format': '%Y-%m-%d',
            'output_format': '%m/%d/%Y'
        })  # Date column
        
        layout.addWidget(self.table)
        
        # Status label
        self.status_label = QLabel("Ready - Try the Excel-like features!")
        self.status_label.setStyleSheet("margin: 5px; font-weight: bold;")
        layout.addWidget(self.status_label)
        
        # Connect signals
        self.table.data_changed.connect(self.on_data_changed)
        
        print("✓ Excel features test setup complete")
    
    def setup_fallback(self):
        """Setup fallback when Excel features not available"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        label = QLabel("Excel-like features not available - check imports")
        label.setAlignment(Qt.AlignCenter)
        label.setStyleSheet("color: red; font-size: 16px; margin: 20px;")
        layout.addWidget(label)
    
    def populate_sample_data(self):
        """Populate table with sample data"""
        sample_data = [
            ["Laptop", "999.99", "5", "2024-01-15", "Electronics", "High-end laptop"],
            ["Mouse", "29.99", "20", "2024-01-16", "Electronics", "Wireless mouse"],
            ["Keyboard", "79.99", "15", "2024-01-17", "Electronics", "Mechanical keyboard"],
            ["Monitor", "299.99", "8", "2024-01-18", "Electronics", "24-inch display"],
            ["Desk", "199.99", "3", "2024-01-19", "Furniture", "Standing desk"],
            ["Chair", "149.99", "7", "2024-01-20", "Furniture", "Ergonomic chair"],
            ["Book", "19.99", "50", "2024-01-21", "Books", "Programming guide"],
            ["Pen", "2.99", "100", "2024-01-22", "Office", "Blue ink pen"],
            ["Notebook", "9.99", "25", "2024-01-23", "Office", "Spiral notebook"],
            ["Coffee", "12.99", "30", "2024-01-24", "Food", "Premium coffee beans"],
        ]
        
        for row, row_data in enumerate(sample_data):
            for col, cell_data in enumerate(row_data):
                from PyQt5.QtWidgets import QTableWidgetItem
                item = QTableWidgetItem(str(cell_data))
                self.table.setItem(row, col, item)
        
        self.status_label.setText("Sample data populated - try Excel-like features!")
    
    def test_validation(self):
        """Test validation by adding invalid data"""
        from PyQt5.QtWidgets import QTableWidgetItem
        
        # Add invalid data to test validation
        invalid_data = [
            ["", "invalid_price", "invalid_qty", "invalid_date", "Test", "Invalid data test"],
        ]
        
        row = self.table.rowCount() - 1
        for col, cell_data in enumerate(invalid_data[0]):
            item = QTableWidgetItem(str(cell_data))
            self.table.setItem(row, col, item)
        
        self.status_label.setText("Invalid data added - check validation highlighting!")
    
    def clear_table(self):
        """Clear all table data"""
        self.table.clearContents()
        self.status_label.setText("Table cleared")
    
    def on_data_changed(self):
        """Handle data changes"""
        self.status_label.setText("Data changed - auto-save will trigger in 30 seconds")


def test_excel_features():
    """Test Excel-like features"""
    print("\n=== Testing Excel-like Features ===")
    
    if not EXCEL_FEATURES_AVAILABLE:
        print("✗ Excel-like features not available")
        return False
    
    try:
        app = QApplication.instance() or QApplication([])
        
        # Test ExcelLikeTableWidget
        table = ExcelLikeTableWidget()
        table.setRowCount(5)
        table.setColumnCount(3)
        print("✓ ExcelLikeTableWidget created")
        
        # Test EnhancedDataTable
        enhanced_table = EnhancedDataTable()
        enhanced_table.add_validation_rule(0, 'required')
        enhanced_table.add_column_format(1, 'currency')
        print("✓ EnhancedDataTable with validation and formatting created")
        
        # Test keyboard shortcuts
        print("✓ Keyboard shortcuts configured:")
        print("  - Tab/Shift+Tab navigation")
        print("  - Enter for next row")
        print("  - Ctrl+C/V for copy/paste")
        print("  - Ctrl+D for fill down")
        print("  - Ctrl+R for fill right")
        print("  - Context menu with Excel-like options")
        
        return True
        
    except Exception as e:
        print(f"✗ Error testing Excel features: {e}")
        return False


def main():
    """Main test function"""
    print("=== Report Maker v2 Excel Features Test ===")
    
    # Test features
    features_ok = test_excel_features()
    
    # Create and run test application
    app = QApplication.instance() or QApplication(sys.argv)
    
    window = ExcelFeaturesTestWindow()
    window.show()
    
    print(f"\n=== Test Results ===")
    print(f"Excel Features Available: {EXCEL_FEATURES_AVAILABLE}")
    print(f"Features Test: {'✓ PASS' if features_ok else '✗ FAIL'}")
    
    if EXCEL_FEATURES_AVAILABLE:
        print("\n=== Excel Features Test Instructions ===")
        print("1. Click 'Populate Sample Data' to add test data")
        print("2. Try Tab/Shift+Tab to navigate between cells")
        print("3. Use Enter to move to the next row")
        print("4. Select cells and use Ctrl+C to copy, Ctrl+V to paste")
        print("5. Select a range and use Ctrl+D to fill down")
        print("6. Select a range and use Ctrl+R to fill right")
        print("7. Right-click for context menu with Excel-like options")
        print("8. Click 'Test Validation' to see validation in action")
        print("9. Try Ctrl+Shift+A to auto-fit columns")
        print("10. Data auto-saves every 30 seconds")
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()

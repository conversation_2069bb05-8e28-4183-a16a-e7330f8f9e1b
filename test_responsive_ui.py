#!/usr/bin/env python3
"""
Test script for the responsive UI improvements in Report Maker v2
This script tests the responsive design and Excel-like functionality
"""

import sys
import os
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QLineEdit, QComboBox,
    QTableWidgetItem
)
from PyQt5.QtCore import Qt

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from responsive_ui import (
        ResponsiveWidget, ExcelLikeTableWidget, apply_responsive_stylesheet,
        ResponsiveSplitter, ScrollableContainer
    )
    from enhanced_widgets import (
        EnhancedDataTable, ResponsiveFormWidget, EnhancedProgressWidget,
        ResponsiveFileSelector
    )
    print("✓ Responsive UI components imported successfully")
    RESPONSIVE_UI_AVAILABLE = True
except ImportError as e:
    print(f"✗ Failed to import responsive UI components: {e}")
    RESPONSIVE_UI_AVAILABLE = False


class TestWindow(QMainWindow):
    """Test window to demonstrate responsive UI features"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Report Maker v2 - Responsive UI Test")
        self.setMinimumSize(320, 480)
        self.resize(1200, 800)
        
        if RESPONSIVE_UI_AVAILABLE:
            self.setStyleSheet(apply_responsive_stylesheet())
            self.setup_responsive_test()
        else:
            self.setup_fallback_test()
    
    def setup_responsive_test(self):
        """Setup test with responsive components"""
        # Central widget
        central_widget = ResponsiveWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Title
        title = QLabel("Responsive UI Test - Resize window to see responsive behavior")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px;")
        layout.addWidget(title)
        
        # Responsive splitter
        splitter = ResponsiveSplitter()
        layout.addWidget(splitter)
        
        # Left panel with form
        left_panel = ResponsiveWidget()
        left_layout = QVBoxLayout(left_panel)
        
        # Test form
        form = ResponsiveFormWidget()
        form.add_form_field("Name", QLineEdit(), required=True)
        form.add_form_field("Email", QLineEdit(), required=True)
        form.add_form_field("Category", QComboBox())
        left_layout.addWidget(form)
        
        # File selector
        file_selector = ResponsiveFileSelector(file_filter="All Files (*)")
        left_layout.addWidget(file_selector)
        
        # Progress widget
        progress = EnhancedProgressWidget()
        progress.set_progress(65, 100, "Processing...", "Testing responsive progress widget")
        left_layout.addWidget(progress)
        
        splitter.addWidget(left_panel)
        
        # Right panel with Excel-like table
        right_panel = ResponsiveWidget()
        right_layout = QVBoxLayout(right_panel)
        
        table_label = QLabel("Excel-like Table (Try Tab, Enter, Ctrl+C/V, right-click)")
        table_label.setStyleSheet("font-weight: bold;")
        right_layout.addWidget(table_label)
        
        # Enhanced data table
        table = EnhancedDataTable(auto_save_file="test_autosave.json")
        table.setRowCount(10)
        table.setColumnCount(5)
        table.setHorizontalHeaderLabels(["Name", "Value", "Category", "Date", "Notes"])
        
        # Add some sample data
        sample_data = [
            ["Item 1", "100", "Type A", "2024-01-01", "Sample note"],
            ["Item 2", "200", "Type B", "2024-01-02", "Another note"],
            ["Item 3", "300", "Type A", "2024-01-03", "Third note"],
        ]
        
        for row, row_data in enumerate(sample_data):
            for col, cell_data in enumerate(row_data):
                item = QTableWidgetItem(str(cell_data))
                table.setItem(row, col, item)
        
        # Add validation rules
        table.add_validation_rule(1, 'numeric', error_message="Value must be numeric")
        table.add_validation_rule(0, 'required', error_message="Name is required")
        
        right_layout.addWidget(table)
        splitter.addWidget(right_panel)
        
        # Set initial splitter sizes
        splitter.setSizes([400, 600])
        
        print("✓ Responsive test UI setup complete")
        print("Features to test:")
        print("  - Resize window to see responsive behavior")
        print("  - Use Tab/Enter navigation in table")
        print("  - Try Ctrl+C/Ctrl+V in table")
        print("  - Right-click table for context menu")
        print("  - Test form validation")
        print("  - Auto-save functionality")
    
    def setup_fallback_test(self):
        """Setup fallback test without responsive components"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        label = QLabel("Responsive UI components not available - running in fallback mode")
        label.setAlignment(Qt.AlignCenter)
        label.setStyleSheet("color: red; font-size: 14px; margin: 20px;")
        layout.addWidget(label)
        
        print("✗ Running in fallback mode - responsive UI not available")


def test_responsive_components(app):
    """Test individual responsive components"""
    print("\n=== Testing Responsive Components ===")

    if not RESPONSIVE_UI_AVAILABLE:
        print("✗ Responsive UI components not available for testing")
        return False

    try:
        # Test ResponsiveWidget
        widget = ResponsiveWidget()
        print("✓ ResponsiveWidget created successfully")

        # Test ExcelLikeTableWidget
        table = ExcelLikeTableWidget()
        table.setRowCount(5)
        table.setColumnCount(3)
        print("✓ ExcelLikeTableWidget created successfully")

        # Test EnhancedDataTable
        enhanced_table = EnhancedDataTable()
        enhanced_table.add_validation_rule(0, 'required')
        print("✓ EnhancedDataTable with validation created successfully")

        # Test ResponsiveFormWidget
        form = ResponsiveFormWidget()
        print("✓ ResponsiveFormWidget created successfully")

        # Test other components
        progress = EnhancedProgressWidget()
        file_selector = ResponsiveFileSelector()
        print("✓ All enhanced widgets created successfully")

        return True

    except Exception as e:
        print(f"✗ Error testing components: {e}")
        return False


def main():
    """Main test function"""
    print("=== Report Maker v2 Responsive UI Test ===")

    # Create and run test application
    app = QApplication(sys.argv)

    # Test component creation
    components_ok = test_responsive_components(app)
    
    # Test window
    window = TestWindow()
    window.show()
    
    print(f"\n=== Test Results ===")
    print(f"Responsive UI Available: {RESPONSIVE_UI_AVAILABLE}")
    print(f"Components Test: {'✓ PASS' if components_ok else '✗ FAIL'}")
    
    if RESPONSIVE_UI_AVAILABLE:
        print("\n=== Instructions ===")
        print("1. Resize the window to test responsive behavior")
        print("2. Try different screen sizes (mobile: <768px, tablet: 768-1024px, desktop: >1024px)")
        print("3. Test Excel-like table features:")
        print("   - Tab/Shift+Tab for cell navigation")
        print("   - Enter to move to next row")
        print("   - Ctrl+C/Ctrl+V for copy/paste")
        print("   - Right-click for context menu")
        print("   - Type invalid data to see validation")
        print("4. Test form validation by leaving required fields empty")
        print("5. Test file selector and progress widgets")
    
    # Run the application
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()

"""
Responsive UI Framework for Report Maker v2
Provides responsive design components and utilities for PyQt5 applications
"""

import sys
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QSplitter, QScrollArea,
    QSizePolicy, QFrame, QApplication, QTableWidget, QTableWidgetItem,
    QHeaderView, QAbstractItemView, QShortcut, QMessageBox, QUndoStack,
    QUndoCommand, QMenu, QAction
)
from PyQt5.QtCore import Qt, QTimer, QSize, pyqtSignal, QMimeData, QEvent
from PyQt5.QtGui import QKeySequence, QClipboard, QFont, QPalette, QColor


class ResponsiveWidget(QWidget):
    """Base responsive widget that adapts to screen size changes"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.min_width = 320
        self.tablet_width = 768
        self.desktop_width = 1024
        self.large_desktop_width = 1920
        
        # Set responsive size policies
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        
        # Connect to application screen changes
        if QApplication.instance():
            QApplication.instance().primaryScreen().geometryChanged.connect(self.on_screen_changed)
    
    def on_screen_changed(self):
        """Handle screen geometry changes"""
        self.update_responsive_layout()
    
    def update_responsive_layout(self):
        """Update layout based on current screen size - override in subclasses"""
        pass
    
    def get_screen_category(self):
        """Get current screen size category"""
        width = self.width()
        if width < self.tablet_width:
            return "mobile"
        elif width < self.desktop_width:
            return "tablet"
        elif width < self.large_desktop_width:
            return "desktop"
        else:
            return "large_desktop"
    
    def resizeEvent(self, event):
        """Handle resize events"""
        super().resizeEvent(event)
        self.update_responsive_layout()


class ResponsiveLayout(QVBoxLayout):
    """Responsive layout that adapts to screen size"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setContentsMargins(10, 10, 10, 10)
        self.setSpacing(10)
    
    def add_responsive_widget(self, widget, mobile_stretch=1, tablet_stretch=1, desktop_stretch=1):
        """Add widget with different stretch factors for different screen sizes"""
        self.addWidget(widget)
        # Store stretch factors for later use
        widget.mobile_stretch = mobile_stretch
        widget.tablet_stretch = tablet_stretch
        widget.desktop_stretch = desktop_stretch


class ResponsiveSplitter(QSplitter):
    """Splitter that adapts orientation based on screen size"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.mobile_orientation = Qt.Vertical
        self.desktop_orientation = Qt.Horizontal
        self.tablet_width = 768
        
    def resizeEvent(self, event):
        """Handle resize events to change orientation"""
        super().resizeEvent(event)
        width = self.width()
        
        if width < self.tablet_width:
            if self.orientation() != self.mobile_orientation:
                self.setOrientation(self.mobile_orientation)
        else:
            if self.orientation() != self.desktop_orientation:
                self.setOrientation(self.desktop_orientation)


class ScrollableContainer(QScrollArea):
    """Scrollable container for responsive content"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWidgetResizable(True)
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.setFrameStyle(QFrame.NoFrame)
        
        # Create content widget
        self.content_widget = ResponsiveWidget()
        self.content_layout = ResponsiveLayout(self.content_widget)
        self.setWidget(self.content_widget)
    
    def add_content(self, widget):
        """Add content to the scrollable area"""
        self.content_layout.addWidget(widget)


class ExcelLikeTableWidget(QTableWidget):
    """Enhanced table widget with Excel-like functionality"""
    
    data_changed = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_excel_features()
        self.setup_auto_save()
        self.undo_stack = QUndoStack(self)
        
    def setup_excel_features(self):
        """Setup Excel-like features"""
        # Enable selection of cells, rows, and columns
        self.setSelectionBehavior(QAbstractItemView.SelectItems)
        self.setSelectionMode(QAbstractItemView.ExtendedSelection)
        
        # Enable editing
        self.setEditTriggers(QAbstractItemView.DoubleClicked | 
                           QAbstractItemView.EditKeyPressed |
                           QAbstractItemView.AnyKeyPressed)
        
        # Setup keyboard shortcuts
        self.setup_shortcuts()
        
        # Enable drag and drop
        self.setDragDropMode(QAbstractItemView.InternalMove)
        
        # Connect signals
        self.itemChanged.connect(self.on_item_changed)
        
        # Set up context menu
        self.setContextMenuPolicy(Qt.CustomContextMenu)
        self.customContextMenuRequested.connect(self.show_context_menu)
        
        # Resize columns to content
        self.horizontalHeader().setSectionResizeMode(QHeaderView.Interactive)
        self.horizontalHeader().setStretchLastSection(True)
        
    def setup_shortcuts(self):
        """Setup keyboard shortcuts for Excel-like navigation"""
        # Copy (Ctrl+C)
        copy_shortcut = QShortcut(QKeySequence.Copy, self)
        copy_shortcut.activated.connect(self.copy_selection)
        
        # Paste (Ctrl+V)
        paste_shortcut = QShortcut(QKeySequence.Paste, self)
        paste_shortcut.activated.connect(self.paste_selection)
        
        # Cut (Ctrl+X)
        cut_shortcut = QShortcut(QKeySequence.Cut, self)
        cut_shortcut.activated.connect(self.cut_selection)
        
        # Undo (Ctrl+Z)
        undo_shortcut = QShortcut(QKeySequence.Undo, self)
        undo_shortcut.activated.connect(self.undo_stack.undo)
        
        # Redo (Ctrl+Y)
        redo_shortcut = QShortcut(QKeySequence.Redo, self)
        redo_shortcut.activated.connect(self.undo_stack.redo)
        
        # Delete (Del)
        delete_shortcut = QShortcut(QKeySequence.Delete, self)
        delete_shortcut.activated.connect(self.delete_selection)
        
    def setup_auto_save(self):
        """Setup auto-save functionality"""
        self.auto_save_timer = QTimer()
        self.auto_save_timer.timeout.connect(self.auto_save)
        self.auto_save_timer.start(30000)  # Auto-save every 30 seconds
        
    def keyPressEvent(self, event):
        """Handle key press events for Excel-like navigation"""
        if event.key() == Qt.Key_Tab:
            # Tab to next cell
            self.move_to_next_cell()
            event.accept()
        elif event.key() == Qt.Key_Backtab:
            # Shift+Tab to previous cell
            self.move_to_previous_cell()
            event.accept()
        elif event.key() == Qt.Key_Return or event.key() == Qt.Key_Enter:
            # Enter to next row
            self.move_to_next_row()
            event.accept()
        elif event.key() == Qt.Key_Escape:
            # Escape to cancel editing
            self.clearSelection()
            event.accept()
        else:
            super().keyPressEvent(event)
    
    def move_to_next_cell(self):
        """Move to the next cell (Tab navigation)"""
        current = self.currentItem()
        if current:
            row = current.row()
            col = current.column()
            
            # Move to next column, or next row if at end
            if col < self.columnCount() - 1:
                self.setCurrentCell(row, col + 1)
            elif row < self.rowCount() - 1:
                self.setCurrentCell(row + 1, 0)
            else:
                # Add new row if at the end
                self.add_new_row()
                self.setCurrentCell(row + 1, 0)
    
    def move_to_previous_cell(self):
        """Move to the previous cell (Shift+Tab navigation)"""
        current = self.currentItem()
        if current:
            row = current.row()
            col = current.column()
            
            # Move to previous column, or previous row if at beginning
            if col > 0:
                self.setCurrentCell(row, col - 1)
            elif row > 0:
                self.setCurrentCell(row - 1, self.columnCount() - 1)
    
    def move_to_next_row(self):
        """Move to the next row (Enter navigation)"""
        current = self.currentItem()
        if current:
            row = current.row()
            col = current.column()
            
            if row < self.rowCount() - 1:
                self.setCurrentCell(row + 1, col)
            else:
                # Add new row if at the end
                self.add_new_row()
                self.setCurrentCell(row + 1, col)
    
    def add_new_row(self):
        """Add a new row to the table"""
        row_count = self.rowCount()
        self.insertRow(row_count)

        # Initialize cells in the new row
        for col in range(self.columnCount()):
            item = QTableWidgetItem("")
            self.setItem(row_count, col, item)

    def copy_selection(self):
        """Copy selected cells to clipboard"""
        selection = self.selectedRanges()
        if not selection:
            return

        # Get the selected range
        selected_range = selection[0]
        top_row = selected_range.topRow()
        bottom_row = selected_range.bottomRow()
        left_col = selected_range.leftColumn()
        right_col = selected_range.rightColumn()

        # Build clipboard text
        clipboard_text = []
        for row in range(top_row, bottom_row + 1):
            row_data = []
            for col in range(left_col, right_col + 1):
                item = self.item(row, col)
                cell_text = item.text() if item else ""
                row_data.append(cell_text)
            clipboard_text.append("\t".join(row_data))

        # Copy to clipboard
        clipboard = QApplication.clipboard()
        clipboard.setText("\n".join(clipboard_text))

    def paste_selection(self):
        """Paste from clipboard to selected cells"""
        clipboard = QApplication.clipboard()
        clipboard_text = clipboard.text()

        if not clipboard_text:
            return

        current = self.currentItem()
        if not current:
            return

        start_row = current.row()
        start_col = current.column()

        # Parse clipboard data
        rows = clipboard_text.split('\n')
        for row_offset, row_data in enumerate(rows):
            if not row_data.strip():
                continue

            cells = row_data.split('\t')
            target_row = start_row + row_offset

            # Add rows if necessary
            while target_row >= self.rowCount():
                self.add_new_row()

            for col_offset, cell_data in enumerate(cells):
                target_col = start_col + col_offset

                # Skip if column is out of bounds
                if target_col >= self.columnCount():
                    continue

                # Set cell data
                item = self.item(target_row, target_col)
                if not item:
                    item = QTableWidgetItem(cell_data)
                    self.setItem(target_row, target_col, item)
                else:
                    item.setText(cell_data)

    def cut_selection(self):
        """Cut selected cells to clipboard"""
        self.copy_selection()
        self.delete_selection()

    def delete_selection(self):
        """Delete content of selected cells"""
        for item in self.selectedItems():
            item.setText("")

    def show_context_menu(self, position):
        """Show context menu"""
        menu = QMenu(self)

        copy_action = QAction("Copy", self)
        copy_action.triggered.connect(self.copy_selection)
        menu.addAction(copy_action)

        paste_action = QAction("Paste", self)
        paste_action.triggered.connect(self.paste_selection)
        menu.addAction(paste_action)

        cut_action = QAction("Cut", self)
        cut_action.triggered.connect(self.cut_selection)
        menu.addAction(cut_action)

        delete_action = QAction("Delete", self)
        delete_action.triggered.connect(self.delete_selection)
        menu.addAction(delete_action)

        menu.addSeparator()

        add_row_action = QAction("Add Row", self)
        add_row_action.triggered.connect(self.add_new_row)
        menu.addAction(add_row_action)

        menu.exec_(self.mapToGlobal(position))

    def on_item_changed(self, item):
        """Handle item changes for auto-save"""
        self.data_changed.emit()

    def auto_save(self):
        """Auto-save functionality - override in subclasses"""
        # This should be implemented by subclasses to save data
        pass


class CellEditCommand(QUndoCommand):
    """Undo command for cell editing"""

    def __init__(self, table, row, col, old_text, new_text):
        super().__init__()
        self.table = table
        self.row = row
        self.col = col
        self.old_text = old_text
        self.new_text = new_text
        self.setText(f"Edit cell ({row}, {col})")

    def undo(self):
        item = self.table.item(self.row, self.col)
        if item:
            item.setText(self.old_text)

    def redo(self):
        item = self.table.item(self.row, self.col)
        if item:
            item.setText(self.new_text)


def apply_responsive_stylesheet():
    """Apply responsive stylesheet to the application"""
    return """
    /* Main Application Styling */
    QMainWindow {
        background-color: #f5f5f5;
        color: #333333;
    }

    /* Group Box Styling */
    QGroupBox {
        font-weight: bold;
        border: 2px solid #cccccc;
        border-radius: 8px;
        margin-top: 1ex;
        padding-top: 10px;
        background-color: white;
    }

    QGroupBox::title {
        subcontrol-origin: margin;
        left: 10px;
        padding: 0 5px 0 5px;
        color: #2c3e50;
    }

    /* Button Styling */
    QPushButton {
        background-color: #3498db;
        border: none;
        color: white;
        padding: 8px 16px;
        border-radius: 4px;
        font-weight: bold;
        min-height: 20px;
    }

    QPushButton:hover {
        background-color: #2980b9;
    }

    QPushButton:pressed {
        background-color: #21618c;
    }

    QPushButton:disabled {
        background-color: #bdc3c7;
        color: #7f8c8d;
    }

    /* Table Styling */
    QTableWidget {
        gridline-color: #ddd;
        background-color: white;
        alternate-background-color: #f9f9f9;
        selection-background-color: #3498db;
        border: 1px solid #ddd;
        border-radius: 4px;
    }

    QTableWidget::item {
        padding: 4px;
        border: none;
    }

    QTableWidget::item:selected {
        background-color: #3498db;
        color: white;
    }

    QHeaderView::section {
        background-color: #ecf0f1;
        padding: 6px;
        border: 1px solid #bdc3c7;
        font-weight: bold;
        color: #2c3e50;
    }

    /* Input Field Styling */
    QLineEdit, QComboBox {
        padding: 6px;
        border: 2px solid #ddd;
        border-radius: 4px;
        background-color: white;
        min-height: 16px;
    }

    QLineEdit:focus, QComboBox:focus {
        border-color: #3498db;
    }

    /* Label Styling */
    QLabel {
        color: #2c3e50;
    }

    /* Progress Bar Styling */
    QProgressBar {
        border: 2px solid #ddd;
        border-radius: 4px;
        text-align: center;
        background-color: #ecf0f1;
    }

    QProgressBar::chunk {
        background-color: #27ae60;
        border-radius: 2px;
    }

    /* Scroll Bar Styling */
    QScrollBar:vertical {
        background-color: #f1f1f1;
        width: 12px;
        border-radius: 6px;
    }

    QScrollBar::handle:vertical {
        background-color: #c1c1c1;
        border-radius: 6px;
        min-height: 20px;
    }

    QScrollBar::handle:vertical:hover {
        background-color: #a1a1a1;
    }

    /* Responsive breakpoints */
    @media (max-width: 768px) {
        QPushButton {
            padding: 10px 12px;
            font-size: 14px;
        }

        QGroupBox {
            margin: 5px;
            padding: 5px;
        }
    }

    @media (min-width: 1920px) {
        QPushButton {
            padding: 12px 20px;
            font-size: 16px;
        }

        QLabel {
            font-size: 14px;
        }
    }
    """
